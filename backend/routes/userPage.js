const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getUserPage,
  publishUserPage,
  unpublishUserPage,
  getPublishedPages,
  getPageStatus
} = require('../controllers/userPageController');

// @route   GET /api/user-page/published
// @desc    Get all published pages (for listings)
// @access  Public
router.get('/published', getPublishedPages);

// @route   GET /api/user-page/status
// @desc    Get page status and completion for dashboard
// @access  Private
router.get('/status', protect, getPageStatus);

// @route   PUT /api/user-page/publish
// @desc    Publish user's page
// @access  Private
router.put('/publish', protect, publishUserPage);

// @route   PUT /api/user-page/unpublish
// @desc    Unpublish user's page (move to draft)
// @access  Private
router.put('/unpublish', protect, unpublishUserPage);

// @route   GET /api/user-page/:identifier
// @desc    Get user's complete page data (by userId or username)
// @access  Public (if published) / Private (if owner)
router.get('/:identifier', optionalAuth, getUserPage);

module.exports = router;
