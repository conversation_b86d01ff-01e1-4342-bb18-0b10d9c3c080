const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getListings,
  getListingById,
  createListing,
  updateListing,
  deleteListing,
  getFilterOptions,
  getMyListings,
  publishListing,
  unpublishListing
} = require('../controllers/listingController');

// @route   GET /api/listings
// @desc    Get all public listings with filters
// @access  Public
router.get('/', getListings);

// @route   GET /api/listings/filters
// @desc    Get filter options
// @access  Public
router.get('/filters', getFilterOptions);

// @route   GET /api/listings/my-listings
// @desc    Get user's listings
// @access  Private
router.get('/my-listings', protect, getMyListings);

// @route   GET /api/listings/:id
// @desc    Get single listing by ID
// @access  Public
router.get('/:id', getListingById);

// @route   POST /api/listings
// @desc    Create new listing
// @access  Private
router.post('/', protect, createListing);

// @route   PUT /api/listings/:id
// @desc    Update listing
// @access  Private
router.put('/:id', protect, updateListing);

// @route   DELETE /api/listings/:id
// @desc    Delete listing
// @access  Private
router.delete('/:id', protect, deleteListing);

// @route   PUT /api/listings/:id/publish
// @desc    Publish listing
// @access  Private
router.put('/:id/publish', protect, publishListing);

// @route   PUT /api/listings/:id/unpublish
// @desc    Unpublish listing (move to draft)
// @access  Private
router.put('/:id/unpublish', protect, unpublishListing);

module.exports = router;
