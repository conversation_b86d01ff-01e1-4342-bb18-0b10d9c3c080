const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { protect } = require('../middleware/auth');

// Configure multer for photo uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = 'uploads/gallery';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'photo-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});
const {
  getAlbums,
  createAlbum,
  getAlbumMedia,
  uploadPhotos,
  addVideo,
  deleteAlbum,
  deleteMedia,
  getPublicAlbums
} = require('../controllers/galleryController');

// @route   GET /api/gallery/albums
// @desc    Get all albums for current user
// @access  Private
router.get('/albums', protect, getAlbums);

// @route   POST /api/gallery/albums
// @desc    Create a new album
// @access  Private
router.post('/albums', protect, createAlbum);

// @route   GET /api/gallery/albums/:albumId/media
// @desc    Get album with all media
// @access  Private
router.get('/albums/:albumId/media', protect, getAlbumMedia);

// @route   POST /api/gallery/albums/:albumId/photos
// @desc    Upload photos to a specific album
// @access  Private
router.post('/albums/:albumId/photos', protect, upload.array('photos', 10), async (req, res) => {
  try {
    const Album = require('../models/Album');
    const Media = require('../models/Media');

    const { albumId } = req.params;
    const { title, description } = req.body;

    console.log('Upload request:', { albumId, title, description, filesCount: req.files?.length });
    console.log('req.files:', req.files);
    console.log('req.body:', req.body);

    // Verify album exists and belongs to user
    const album = await Album.findOne({ _id: albumId, userId: req.user.id });
    if (!album) {
      return res.status(404).json({ message: 'Album not found' });
    }

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: 'No files uploaded' });
    }

    // Create media entries for uploaded files
    const mediaItems = [];
    for (const file of req.files) {
      console.log('Processing file:', file);
      console.log('File properties:', {
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype,
        originalname: file.originalname
      });

      const media = new Media({
        userId: req.user.id,
        albumId: albumId,
        type: 'photo',
        title: title || file.originalname,
        description: description || '',
        url: `/uploads/gallery/${file.filename}`,
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype
      });

      await media.save();
      mediaItems.push(media);
    }

    // Update album media count and cover image
    album.mediaCount = await Media.countDocuments({ albumId: albumId });
    if (!album.coverImage && mediaItems.length > 0) {
      album.coverImage = mediaItems[0].url;
    }
    await album.save();

    res.json({
      success: true,
      message: `${mediaItems.length} photo(s) uploaded successfully`,
      media: mediaItems
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ message: 'Server error during upload' });
  }
});

// @route   DELETE /api/gallery/albums/:albumId
// @desc    Delete an album and all its media
// @access  Private
router.delete('/albums/:albumId', protect, deleteAlbum);

// @route   POST /api/gallery/photos
// @desc    Upload photos to an album
// @access  Private
router.post('/photos', protect, uploadPhotos);

// @route   GET /api/gallery/videos
// @desc    Get all videos for current user
// @access  Private
router.get('/videos', protect, async (req, res) => {
  try {
    const Media = require('../models/Media');
    const videos = await Media.find({
      userId: req.user.id,
      type: 'video'
    }).sort({ createdAt: -1 });

    res.json({
      success: true,
      videos
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/gallery/videos
// @desc    Add video to gallery
// @access  Private
router.post('/videos', protect, async (req, res) => {
  try {
    const Media = require('../models/Media');
    const { title, description, url, platform } = req.body;

    console.log('Video upload request:', { title, description, url, platform });

    if (!url || !url.trim()) {
      return res.status(400).json({ message: 'Video URL is required' });
    }

    if (!title || !title.trim()) {
      return res.status(400).json({ message: 'Video title is required' });
    }

    // Validate platform
    const validPlatforms = ['youtube', 'vimeo', 'instagram', 'facebook'];
    if (!validPlatforms.includes(platform)) {
      return res.status(400).json({ message: 'Invalid platform selected' });
    }

    // Validate URL based on platform
    let isValidUrl = false;
    switch (platform) {
      case 'youtube':
        isValidUrl = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/.test(url);
        break;
      case 'vimeo':
        isValidUrl = /vimeo\.com\/(\d+)/.test(url);
        break;
      case 'facebook':
        isValidUrl = url.includes('facebook.com');
        break;
      case 'instagram':
        isValidUrl = url.includes('instagram.com');
        break;
    }

    if (!isValidUrl) {
      return res.status(400).json({
        message: `Invalid ${platform} URL format`
      });
    }

    // Extract video ID for supported platforms
    let videoId = '';
    switch (platform) {
      case 'youtube':
        const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        videoId = youtubeMatch ? youtubeMatch[1] : '';
        break;
      case 'vimeo':
        const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
        videoId = vimeoMatch ? vimeoMatch[1] : '';
        break;
      default:
        videoId = url;
    }

    const video = new Media({
      type: 'video',
      userId: req.user.id,
      title: title.trim(),
      description: description?.trim() || '',
      videoUrl: url.trim(),
      platform: platform,
      videoId: videoId,
      // Required fields for Media model (set defaults for video type)
      url: url.trim(), // Use video URL as the main URL
      filename: `${platform}-${videoId || Date.now()}`,
      size: 0, // Videos don't have file size
      mimetype: 'video/url' // Custom mimetype for video URLs
    });

    await video.save();

    res.status(201).json({
      success: true,
      message: 'Video added successfully',
      video
    });
  } catch (error) {
    console.error('Error adding video:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/gallery/media/:mediaId
// @desc    Delete a media item
// @access  Private
router.delete('/media/:mediaId', protect, async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const Media = require('../models/Media');
    const Album = require('../models/Album');

    const { mediaId } = req.params;

    // Find the media item
    const media = await Media.findOne({ _id: mediaId, userId: req.user.id });
    if (!media) {
      return res.status(404).json({ message: 'Photo not found' });
    }

    // Delete the physical file
    if (media.url) {
      const filePath = path.join(__dirname, '..', media.url);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // Delete from database
    await Media.findByIdAndDelete(mediaId);

    // Update album media count and cover image
    const album = await Album.findById(media.albumId);
    if (album) {
      album.mediaCount = await Media.countDocuments({ albumId: media.albumId });

      // If deleted photo was cover image, set new cover
      if (album.coverImage === media.url) {
        const firstMedia = await Media.findOne({ albumId: media.albumId }).sort({ createdAt: 1 });
        album.coverImage = firstMedia ? firstMedia.url : null;
      }

      await album.save();
    }

    res.json({ success: true, message: 'Photo deleted successfully' });
  } catch (error) {
    console.error('Error deleting photo:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/gallery/media/:mediaId
// @desc    Update a media item
// @access  Private
router.put('/media/:mediaId', protect, async (req, res) => {
  try {
    const Media = require('../models/Media');
    const { mediaId } = req.params;
    const { title, description } = req.body;

    const media = await Media.findOne({ _id: mediaId, userId: req.user.id });
    if (!media) {
      return res.status(404).json({ message: 'Photo not found' });
    }

    media.title = title || media.title;
    media.description = description || media.description;

    await media.save();

    res.json({ success: true, media, message: 'Photo updated successfully' });
  } catch (error) {
    console.error('Error updating photo:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/gallery/albums/:albumId
// @desc    Update an album
// @access  Private
router.put('/albums/:albumId', protect, async (req, res) => {
  try {
    const Album = require('../models/Album');
    const { albumId } = req.params;
    const { name, description } = req.body;

    const album = await Album.findOne({ _id: albumId, userId: req.user.id });
    if (!album) {
      return res.status(404).json({ message: 'Album not found' });
    }

    album.name = name || album.name;
    album.description = description || album.description;

    await album.save();

    res.json({ success: true, album, message: 'Album updated successfully' });
  } catch (error) {
    console.error('Error updating album:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/gallery/albums/:albumId
// @desc    Delete an album and all its media
// @access  Private
router.delete('/albums/:albumId', protect, async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const Media = require('../models/Media');
    const Album = require('../models/Album');

    const { albumId } = req.params;

    // Find the album
    const album = await Album.findOne({ _id: albumId, userId: req.user.id });
    if (!album) {
      return res.status(404).json({ message: 'Album not found' });
    }

    // Find all media in the album
    const mediaItems = await Media.find({ albumId: albumId });

    // Delete all physical files
    for (const media of mediaItems) {
      if (media.url) {
        const filePath = path.join(__dirname, '..', media.url);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
    }

    // Delete all media from database
    await Media.deleteMany({ albumId: albumId });

    // Delete the album
    await Album.findByIdAndDelete(albumId);

    res.json({ success: true, message: 'Album and all photos deleted successfully' });
  } catch (error) {
    console.error('Error deleting album:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/gallery/public/:userId
// @desc    Get public albums for a user
// @access  Public
router.get('/public/:userId', getPublicAlbums);

// @route   PUT /api/gallery/videos/:videoId
// @desc    Update a video
// @access  Private
router.put('/videos/:videoId', protect, async (req, res) => {
  try {
    const Media = require('../models/Media');
    const { videoId } = req.params;
    const { title, description, url, platform } = req.body;

    console.log('Video update request:', { videoId, title, description, url, platform });

    const video = await Media.findOne({ _id: videoId, userId: req.user.id, type: 'video' });
    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Validate platform and URL if provided
    if (url) {
      const validPlatforms = ['youtube', 'vimeo', 'instagram', 'facebook'];
      if (!validPlatforms.includes(platform)) {
        return res.status(400).json({ message: 'Invalid platform' });
      }

      // Extract video ID for supported platforms
      let videoIdExtracted = '';
      switch (platform) {
        case 'youtube':
          const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
          videoIdExtracted = youtubeMatch ? youtubeMatch[1] : '';
          break;
        case 'vimeo':
          const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
          videoIdExtracted = vimeoMatch ? vimeoMatch[1] : '';
          break;
        default:
          videoIdExtracted = url;
      }

      video.videoUrl = url;
      video.url = url;
      video.platform = platform;
      video.videoId = videoIdExtracted;
      video.filename = `${platform}-${videoIdExtracted || Date.now()}`;
    }

    video.title = title || video.title;
    video.description = description || video.description;

    await video.save();

    res.json({ success: true, video, message: 'Video updated successfully' });
  } catch (error) {
    console.error('Error updating video:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/gallery/videos/:videoId
// @desc    Delete a video
// @access  Private
router.delete('/videos/:videoId', protect, async (req, res) => {
  try {
    const Media = require('../models/Media');
    const { videoId } = req.params;

    const video = await Media.findOne({ _id: videoId, userId: req.user.id, type: 'video' });
    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    await Media.findByIdAndDelete(videoId);

    res.json({ success: true, message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
