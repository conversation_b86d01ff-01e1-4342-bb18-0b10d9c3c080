const Listing = require('../models/Listing');
const User = require('../models/User');

// @desc    Get all public listings with filters
// @route   GET /api/listings
// @access  Public
const getListings = async (req, res) => {
  try {
    const {
      category,
      decorationType,
      murtiType,
      city,
      district,
      state,
      isEcoFriendly,
      isFeatured,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 12
    } = req.query;

    // Build filter object
    const filter = {
      status: 'active',
      isPublic: true
    };

    // Category filter
    if (category && category !== 'all') {
      filter.category = category;
    }

    // Ganesha details filters
    if (decorationType && decorationType !== 'all') {
      filter['ganeshaDetails.decorationType'] = decorationType;
    }

    if (murtiType && murtiType !== 'all') {
      filter['ganeshaDetails.murtiType'] = murtiType;
    }

    if (isEcoFriendly === 'true') {
      filter['ganeshaDetails.isEcoFriendly'] = true;
    }

    // Location filters
    if (city && city !== 'all') {
      filter['location.city'] = new RegExp(city, 'i');
    }

    if (district && district !== 'all') {
      filter['location.district'] = new RegExp(district, 'i');
    }

    if (state && state !== 'all') {
      filter['location.state'] = new RegExp(state, 'i');
    }

    // Featured filter
    if (isFeatured === 'true') {
      filter.isFeatured = true;
    }

    // Search filter
    if (search && search.trim()) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { searchKeywords: new RegExp(search, 'i') },
        { 'location.city': new RegExp(search, 'i') },
        { 'location.district': new RegExp(search, 'i') }
      ];
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    console.log('Listings filter:', filter);
    console.log('Sort options:', sortOptions);

    // Execute query
    const listings = await Listing.find(filter)
      .populate('userId', 'username email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await Listing.countDocuments(filter);

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit));
    const hasNextPage = parseInt(page) < totalPages;
    const hasPrevPage = parseInt(page) > 1;

    res.json({
      success: true,
      listings,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      },
      filters: {
        category,
        decorationType,
        murtiType,
        city,
        district,
        state,
        isEcoFriendly,
        search
      }
    });
  } catch (error) {
    console.error('Error fetching listings:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single listing by ID
// @route   GET /api/listings/:id
// @access  Public
const getListingById = async (req, res) => {
  try {
    const listing = await Listing.findById(req.params.id)
      .populate('userId', 'username email phone')
      .lean();

    if (!listing) {
      return res.status(404).json({ message: 'Listing not found' });
    }

    // Increment view count
    await Listing.findByIdAndUpdate(req.params.id, { $inc: { views: 1 } });

    res.json({
      success: true,
      listing
    });
  } catch (error) {
    console.error('Error fetching listing:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create new listing
// @route   POST /api/listings
// @access  Private
const createListing = async (req, res) => {
  try {
    const listingData = {
      ...req.body,
      userId: req.user.id
    };

    const listing = new Listing(listingData);
    await listing.save();

    const populatedListing = await Listing.findById(listing._id)
      .populate('userId', 'username email');

    res.status(201).json({
      success: true,
      message: 'Listing created successfully',
      listing: populatedListing
    });
  } catch (error) {
    console.error('Error creating listing:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: errors.join(', ') });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update listing
// @route   PUT /api/listings/:id
// @access  Private
const updateListing = async (req, res) => {
  try {
    const listing = await Listing.findById(req.params.id);

    if (!listing) {
      return res.status(404).json({ message: 'Listing not found' });
    }

    // Check if user owns the listing
    if (listing.userId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to update this listing' });
    }

    const updatedListing = await Listing.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('userId', 'username email');

    res.json({
      success: true,
      message: 'Listing updated successfully',
      listing: updatedListing
    });
  } catch (error) {
    console.error('Error updating listing:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: errors.join(', ') });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete listing
// @route   DELETE /api/listings/:id
// @access  Private
const deleteListing = async (req, res) => {
  try {
    const listing = await Listing.findById(req.params.id);

    if (!listing) {
      return res.status(404).json({ message: 'Listing not found' });
    }

    // Check if user owns the listing
    if (listing.userId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to delete this listing' });
    }

    await Listing.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Listing deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting listing:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get filter options
// @route   GET /api/listings/filters
// @access  Public
const getFilterOptions = async (req, res) => {
  try {
    // Get unique values for filters
    const [cities, districts, states, decorationTypes, murtiTypes] = await Promise.all([
      Listing.distinct('location.city', { status: 'active', isPublic: true }),
      Listing.distinct('location.district', { status: 'active', isPublic: true }),
      Listing.distinct('location.state', { status: 'active', isPublic: true }),
      Listing.distinct('ganeshaDetails.decorationType', { status: 'active', isPublic: true }),
      Listing.distinct('ganeshaDetails.murtiType', { status: 'active', isPublic: true })
    ]);

    res.json({
      success: true,
      filters: {
        categories: ['Mandal', 'Home', 'Celebrity'],
        cities: cities.filter(Boolean).sort(),
        districts: districts.filter(Boolean).sort(),
        states: states.filter(Boolean).sort(),
        decorationTypes: decorationTypes.filter(Boolean).sort(),
        murtiTypes: murtiTypes.filter(Boolean).sort()
      }
    });
  } catch (error) {
    console.error('Error fetching filter options:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user's listings
// @route   GET /api/listings/my-listings
// @access  Private
const getMyListings = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const listings = await Listing.find({ userId: req.user.id })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await Listing.countDocuments({ userId: req.user.id });

    res.json({
      success: true,
      listings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user listings:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getListings,
  getListingById,
  createListing,
  updateListing,
  deleteListing,
  getFilterOptions,
  getMyListings
};
