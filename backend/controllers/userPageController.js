const User = require('../models/User');
const MandalInfo = require('../models/MandalInfo');
const GaneshaDetails = require('../models/GaneshaDetails');
const TeamMember = require('../models/TeamMember');
const Media = require('../models/Media');

// @desc    Get user's complete page data (for preview/public view)
// @route   GET /api/user-page/:userId
// @access  Public (if published) / Private (if draft)
const getUserPage = async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Get user basic info
    const user = await User.findById(userId).select('-password -__v');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if page is published or if it's the owner viewing
    const isOwner = req.user && req.user.id === userId;
    if (user.pageStatus !== 'published' && !isOwner) {
      return res.status(404).json({ message: 'Page not found' });
    }

    // Increment view count if it's a public view (not owner)
    if (!isOwner && user.pageStatus === 'published') {
      await User.findByIdAndUpdate(userId, { $inc: { pageViews: 1 } });
    }

    // Get all related data
    const [mandalInfo, ganeshaDetails, teamMembers, photos, videos] = await Promise.all([
      MandalInfo.findOne({ userId }).lean(),
      GaneshaDetails.findOne({ userId }).lean(),
      TeamMember.find({ userId }).sort({ sequence: 1 }).lean(),
      Media.find({ userId, type: 'photo' }).sort({ createdAt: -1 }).lean(),
      Media.find({ userId, type: 'video' }).sort({ createdAt: -1 }).lean()
    ]);

    // Organize photos by album
    const albums = {};
    photos.forEach(photo => {
      const albumName = photo.albumName || 'General';
      if (!albums[albumName]) {
        albums[albumName] = [];
      }
      albums[albumName].push(photo);
    });

    const pageData = {
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        profileImage: user.profileImage,
        coverPhoto: user.coverPhoto,
        category: user.category,
        pageStatus: user.pageStatus,
        publishedAt: user.publishedAt,
        pageViews: user.pageViews,
        createdAt: user.createdAt
      },
      mandalInfo,
      ganeshaDetails,
      teamMembers,
      gallery: {
        albums,
        videos,
        totalPhotos: photos.length,
        totalVideos: videos.length
      },
      stats: {
        totalPhotos: photos.length,
        totalVideos: videos.length,
        teamMembers: teamMembers.length,
        pageViews: user.pageViews
      }
    };

    res.json({
      success: true,
      page: pageData,
      isOwner
    });
  } catch (error) {
    console.error('Error fetching user page:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Publish user's page
// @route   PUT /api/user-page/publish
// @access  Private
const publishUserPage = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Check if user has minimum required data to publish
    const [user, mandalInfo] = await Promise.all([
      User.findById(userId),
      MandalInfo.findOne({ userId })
    ]);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Validation for publishing
    const validationErrors = [];
    
    if (!mandalInfo) {
      validationErrors.push('Mandal information is required');
    }
    
    if (!user.profileImage) {
      validationErrors.push('Profile photo is required');
    }

    if (validationErrors.length > 0) {
      return res.status(400).json({ 
        message: 'Cannot publish page. Missing required information:',
        errors: validationErrors
      });
    }

    // Update user page status
    user.pageStatus = 'published';
    user.publishedAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'Page published successfully!',
      user: {
        pageStatus: user.pageStatus,
        publishedAt: user.publishedAt
      }
    });
  } catch (error) {
    console.error('Error publishing page:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Unpublish user's page (move to draft)
// @route   PUT /api/user-page/unpublish
// @access  Private
const unpublishUserPage = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.pageStatus = 'draft';
    user.publishedAt = null;
    await user.save();

    res.json({
      success: true,
      message: 'Page moved to draft successfully!',
      user: {
        pageStatus: user.pageStatus,
        publishedAt: user.publishedAt
      }
    });
  } catch (error) {
    console.error('Error unpublishing page:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get all published pages (for listings)
// @route   GET /api/user-page/published
// @access  Public
const getPublishedPages = async (req, res) => {
  try {
    const {
      category,
      city,
      district,
      state,
      search,
      sortBy = 'publishedAt',
      sortOrder = 'desc',
      page = 1,
      limit = 12
    } = req.query;

    // Build filter for published pages
    const filter = { pageStatus: 'published' };

    // Category filter
    if (category && category !== 'all') {
      filter.category = category;
    }

    // Search filter
    if (search && search.trim()) {
      const searchRegex = new RegExp(search, 'i');
      filter.$or = [
        { username: searchRegex },
        { 'mandalInfo.mandalName': searchRegex }
      ];
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Aggregation pipeline to get published pages with mandal info
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'mandalinfos',
          localField: '_id',
          foreignField: 'userId',
          as: 'mandalInfo'
        }
      },
      {
        $lookup: {
          from: 'media',
          let: { userId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] }, type: 'photo' } },
            { $limit: 1 }
          ],
          as: 'coverImage'
        }
      },
      {
        $addFields: {
          mandalInfo: { $arrayElemAt: ['$mandalInfo', 0] },
          coverImage: { $arrayElemAt: ['$coverImage', 0] }
        }
      },
      {
        $project: {
          username: 1,
          profileImage: 1,
          coverPhoto: 1,
          category: 1,
          pageViews: 1,
          publishedAt: 1,
          createdAt: 1,
          'mandalInfo.mandalName': 1,
          'mandalInfo.location': 1,
          'mandalInfo.description': 1,
          'coverImage.url': 1
        }
      },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: parseInt(limit) }
    ];

    const [pages, total] = await Promise.all([
      User.aggregate(pipeline),
      User.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit));
    const hasNextPage = parseInt(page) < totalPages;
    const hasPrevPage = parseInt(page) > 1;

    res.json({
      success: true,
      pages,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error) {
    console.error('Error fetching published pages:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get page status and stats for dashboard
// @route   GET /api/user-page/status
// @access  Private
const getPageStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const [user, mandalInfo, ganeshaDetails, teamCount, photoCount, videoCount] = await Promise.all([
      User.findById(userId).select('pageStatus publishedAt pageViews profileImage coverPhoto'),
      MandalInfo.findOne({ userId }),
      GaneshaDetails.findOne({ userId }),
      TeamMember.countDocuments({ userId }),
      Media.countDocuments({ userId, type: 'photo' }),
      Media.countDocuments({ userId, type: 'video' })
    ]);

    const completionStatus = {
      profilePhoto: !!user.profileImage,
      coverPhoto: !!user.coverPhoto,
      mandalInfo: !!mandalInfo,
      ganeshaDetails: !!ganeshaDetails,
      teamMembers: teamCount > 0,
      photos: photoCount > 0,
      videos: videoCount > 0
    };

    const completionPercentage = Math.round(
      (Object.values(completionStatus).filter(Boolean).length / Object.keys(completionStatus).length) * 100
    );

    res.json({
      success: true,
      pageStatus: user.pageStatus,
      publishedAt: user.publishedAt,
      pageViews: user.pageViews,
      completionStatus,
      completionPercentage,
      stats: {
        teamMembers: teamCount,
        photos: photoCount,
        videos: videoCount
      }
    });
  } catch (error) {
    console.error('Error fetching page status:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getUserPage,
  publishUserPage,
  unpublishUserPage,
  getPublishedPages,
  getPageStatus
};
