const mongoose = require('mongoose');

const listingSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  
  // User/Owner Information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  ownerName: {
    type: String,
    required: [true, 'Owner name is required'],
    trim: true
  },
  contactPhone: {
    type: String,
    required: [true, 'Contact phone is required'],
    match: [/^[6-9]\d{9}$/, 'Please enter a valid 10-digit phone number']
  },
  contactEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  
  // Category Information
  category: {
    type: String,
    enum: ['Mandal', 'Home', 'Celebrity'],
    required: [true, 'Category is required'],
    index: true
  },
  
  // Location Information
  location: {
    address: {
      type: String,
      required: [true, 'Address is required'],
      trim: true
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
      index: true
    },
    district: {
      type: String,
      required: [true, 'District is required'],
      trim: true,
      index: true
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
      index: true
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^\d{6}$/, 'Please enter a valid 6-digit pincode']
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Ganesha Details
  ganeshaDetails: {
    height: {
      type: String,
      trim: true
    },
    decorationType: {
      type: String,
      enum: ['Traditional', 'Modern', 'Eco-Friendly', 'Theme-Based', 'Minimalist', 'Grand'],
      index: true
    },
    murtiType: {
      type: String,
      enum: ['Clay', 'Plaster of Paris', 'Eco-Friendly', 'Fiber', 'Metal', 'Stone'],
      index: true
    },
    isEcoFriendly: {
      type: Boolean,
      default: false,
      index: true
    },
    celebrationDuration: {
      startDate: Date,
      endDate: Date,
      days: Number
    }
  },
  
  // Facilities & Features
  facilities: {
    wheelchairAccessible: {
      type: Boolean,
      default: false
    },
    parkingAvailable: {
      type: Boolean,
      default: false
    },
    restrooms: {
      type: Boolean,
      default: false
    },
    foodStalls: {
      type: Boolean,
      default: false
    },
    culturalPrograms: {
      type: Boolean,
      default: false
    },
    onlineAarti: {
      type: Boolean,
      default: false
    }
  },
  
  // Media
  images: [{
    url: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  videos: [{
    url: String,
    platform: String,
    title: String
  }],
  
  // Timing
  visitingHours: {
    openTime: String,
    closeTime: String,
    isOpen24Hours: {
      type: Boolean,
      default: false
    }
  },
  
  // Status & Visibility
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'rejected'],
    default: 'pending',
    index: true
  },
  isPublic: {
    type: Boolean,
    default: true,
    index: true
  },
  isFeatured: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // Engagement
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },
  ratings: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  
  // SEO & Search
  tags: [{
    type: String,
    trim: true
  }],
  searchKeywords: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
listingSchema.index({ category: 1, status: 1, isPublic: 1 });
listingSchema.index({ 'location.city': 1, 'location.state': 1 });
listingSchema.index({ 'ganeshaDetails.decorationType': 1, 'ganeshaDetails.murtiType': 1 });
listingSchema.index({ createdAt: -1 });
listingSchema.index({ views: -1 });
listingSchema.index({ 'ratings.average': -1 });

// Virtual for full address
listingSchema.virtual('fullAddress').get(function() {
  return `${this.location.address}, ${this.location.city}, ${this.location.district}, ${this.location.state} - ${this.location.pincode}`;
});

// Virtual for celebration status
listingSchema.virtual('celebrationStatus').get(function() {
  if (!this.ganeshaDetails.celebrationDuration.startDate || !this.ganeshaDetails.celebrationDuration.endDate) {
    return 'unknown';
  }
  
  const now = new Date();
  const start = new Date(this.ganeshaDetails.celebrationDuration.startDate);
  const end = new Date(this.ganeshaDetails.celebrationDuration.endDate);
  
  if (now < start) return 'upcoming';
  if (now > end) return 'completed';
  return 'ongoing';
});

// Pre-save middleware
listingSchema.pre('save', function(next) {
  // Generate search keywords
  this.searchKeywords = [
    this.title,
    this.description,
    this.category,
    this.location.city,
    this.location.district,
    this.location.state,
    this.ganeshaDetails.decorationType,
    this.ganeshaDetails.murtiType,
    ...this.tags
  ].filter(Boolean).join(' ').toLowerCase();
  
  next();
});

module.exports = mongoose.model('Listing', listingSchema);
