const mongoose = require('mongoose');

const mediaSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['photo', 'video'],
    required: true
  },
  albumId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Album',
    required: function() {
      return this.type === 'photo';
    },
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  // For photos and videos (URL field)
  url: {
    type: String,
    required: true // Required for both photos and videos
  },
  filename: {
    type: String,
    required: function() {
      return this.type === 'photo';
    }
  },
  size: {
    type: Number,
    required: function() {
      return this.type === 'photo';
    },
    default: 0
  },
  mimetype: {
    type: String,
    required: function() {
      return this.type === 'photo';
    },
    default: 'video/url'
  },
  // For videos
  videoUrl: {
    type: String,
    required: function() {
      return this.type === 'video';
    }
  },
  platform: {
    type: String,
    enum: ['youtube', 'vimeo', 'instagram', 'facebook', 'other'],
    required: function() {
      return this.type === 'video';
    }
  },
  videoId: {
    type: String,
    required: function() {
      return this.type === 'video';
    }
  },
  thumbnailUrl: {
    type: String,
    default: null
  },
  // Common fields
  caption: {
    type: String,
    trim: true,
    maxlength: [500, 'Caption cannot exceed 500 characters'],
    default: ''
  },
  tags: [{
    type: String,
    trim: true
  }],
  isPublic: {
    type: Boolean,
    default: true
  },
  views: {
    type: Number,
    default: 0
  },
  likes: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for like count
mediaSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Index for better query performance
mediaSchema.index({ albumId: 1, createdAt: -1 });
mediaSchema.index({ userId: 1, type: 1, createdAt: -1 });
mediaSchema.index({ isPublic: 1, createdAt: -1 });

// Pre-save middleware to extract video information
mediaSchema.pre('validate', function(next) {
  if (this.type === 'video' && this.videoUrl) {
    // Extract platform and video ID
    if (this.videoUrl.includes('youtube.com') || this.videoUrl.includes('youtu.be')) {
      this.platform = 'youtube';
      if (this.videoUrl.includes('youtu.be')) {
        this.videoId = this.videoUrl.split('youtu.be/')[1]?.split('?')[0];
      } else {
        this.videoId = this.videoUrl.split('v=')[1]?.split('&')[0];
      }
      if (this.videoId) {
        this.thumbnailUrl = `https://img.youtube.com/vi/${this.videoId}/maxresdefault.jpg`;
      }
    } else if (this.videoUrl.includes('vimeo.com')) {
      this.platform = 'vimeo';
      this.videoId = this.videoUrl.split('vimeo.com/')[1]?.split('?')[0];
      // Vimeo thumbnail would need API call, so we'll set it later
    } else if (this.videoUrl.includes('instagram.com')) {
      this.platform = 'instagram';
      this.videoId = this.videoUrl.split('/p/')[1]?.split('/')[0] || this.videoUrl.split('/reel/')[1]?.split('/')[0];
    } else if (this.videoUrl.includes('facebook.com')) {
      this.platform = 'facebook';
      this.videoId = this.videoUrl.split('/videos/')[1]?.split('/')[0];
    } else {
      this.platform = 'other';
      this.videoId = this.videoUrl;
    }

    // Ensure videoId is not empty - use a hash of the URL as fallback
    if (!this.videoId || this.videoId.trim() === '') {
      this.videoId = Buffer.from(this.videoUrl).toString('base64').substring(0, 20);
    }
  }
  next();
});

// Post-save middleware to update album media count and cover image
mediaSchema.post('save', async function() {
  const Album = mongoose.model('Album');
  const album = await Album.findById(this.albumId);
  if (album) {
    await album.updateMediaCount();
    if (this.type === 'photo') {
      await album.updateCoverImage();
    }
  }
});

// Post-remove middleware to update album media count
mediaSchema.post('remove', async function() {
  const Album = mongoose.model('Album');
  const album = await Album.findById(this.albumId);
  if (album) {
    await album.updateMediaCount();
    await album.updateCoverImage();
  }
});

// Instance method to increment views
mediaSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save({ validateBeforeSave: false });
};

// Instance method to toggle like
mediaSchema.methods.toggleLike = function(userId) {
  const existingLike = this.likes.find(like => like.userId.toString() === userId.toString());
  
  if (existingLike) {
    // Remove like
    this.likes = this.likes.filter(like => like.userId.toString() !== userId.toString());
  } else {
    // Add like
    this.likes.push({ userId });
  }
  
  return this.save();
};

const Media = mongoose.model('Media', mediaSchema);

module.exports = Media;
