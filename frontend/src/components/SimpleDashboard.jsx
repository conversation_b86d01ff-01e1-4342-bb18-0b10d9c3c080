import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import SimpleMandalInfo from './SimpleMandalInfo';
import SimpleGaneshaDetails from './SimpleGaneshaDetails';
import SimpleTeamManagement from './SimpleTeamManagement';
import SimplePhotoGallery from './SimplePhotoGallery';
import SimpleProfile from './SimpleProfile';
import PagePreview from './PagePreview';

const SimpleDashboard = () => {
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentPage, setCurrentPage] = useState('dashboard');

  console.log('SimpleDashboard: user =', user);

  const menuItems = user?.role === 'ganesha_user' ? [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'mandal-info', label: 'Mandal Info', icon: '🏛️' },
    { id: 'ganesha-details', label: 'Ganesha Details', icon: '🕉️' },
    { id: 'team-management', label: 'Team Management', icon: '👥' },
    { id: 'gallery', label: 'Gallery', icon: '📸' },
    { id: 'page-preview', label: 'Preview My Page', icon: '👁️' },
    { id: 'profile', label: 'Profile', icon: '👤' }
  ] : [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'favorites', label: 'Favorites', icon: '❤️' },
    { id: 'donations', label: 'Donations', icon: '💰' },
    { id: 'profile', label: 'Profile', icon: '👤' }
  ];

  const handleLogout = () => {
    logout();
    window.location.href = '/';
  };

  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div>
            <h2 style={{ color: '#AF0003', marginBottom: '30px' }}>
              Welcome back, {user?.username}! 👋
            </h2>
            
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
              gap: '20px',
              marginBottom: '30px'
            }}>
              {[
                { title: 'Total Visitors', value: '12,345', color: '#AF0003' },
                { title: 'Active Mandals', value: '1,234', color: '#F2A71B' },
                { title: 'Total Devotees', value: '45,678', color: '#10b981' },
                { title: 'Events', value: '567', color: '#3b82f6' }
              ].map((stat, index) => (
                <div key={index} style={{
                  backgroundColor: 'white',
                  padding: '20px',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  textAlign: 'center'
                }}>
                  <h3 style={{ color: stat.color, fontSize: '32px', margin: '0 0 10px 0' }}>
                    {stat.value}
                  </h3>
                  <p style={{ color: '#666', margin: 0 }}>{stat.title}</p>
                </div>
              ))}
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Recent Activities</h3>
              {[
                'New Mandal Registration - Lalbaugcha Raja',
                'Photo Upload - Ganpati Bappa Mandal',
                'Profile Update - Mumbai Cha Raja',
                'Team Member Added - Pune Mandal'
              ].map((activity, index) => (
                <div key={index} style={{
                  padding: '10px 0',
                  borderBottom: index < 3 ? '1px solid #e0e0e0' : 'none',
                  color: '#666'
                }}>
                  {activity}
                </div>
              ))}
            </div>
          </div>
        );
      
      case 'mandal-info':
        return (
          <SimpleMandalInfo onBack={() => setCurrentPage('dashboard')} />
        );
      
      case 'ganesha-details':
        return (
          <SimpleGaneshaDetails onBack={() => setCurrentPage('dashboard')} />
        );
      
      case 'team-management':
        return (
          <SimpleTeamManagement onBack={() => setCurrentPage('dashboard')} />
        );
      
      case 'gallery':
        return (
          <SimplePhotoGallery onBack={() => setCurrentPage('dashboard')} />
        );

      case 'page-preview':
        return (
          <PagePreview onBack={() => setCurrentPage('dashboard')} />
        );

      case 'favorites':
        return (
          <div>
            <h2 style={{ color: '#AF0003', marginBottom: '30px' }}>My Favorites</h2>
            <div style={{
              backgroundColor: 'white',
              padding: '30px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>❤️</div>
                <p>Your favorite Ganesha celebrations will appear here.</p>
                <p>Browse the home page to add favorites!</p>
              </div>
            </div>
          </div>
        );

      case 'donations':
        return (
          <div>
            <h2 style={{ color: '#AF0003', marginBottom: '30px' }}>Donations</h2>
            <div style={{
              backgroundColor: 'white',
              padding: '30px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💰</div>
                <p>Your donation history will appear here.</p>
                <p>Support your favorite Ganesha celebrations!</p>
              </div>
            </div>
          </div>
        );

      case 'profile':
        return (
          <SimpleProfile onBack={() => setCurrentPage('dashboard')} />
        );

      default:
        return <div>Page not found</div>;
    }
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', fontFamily: 'Arial, sans-serif' }}>
      {/* Sidebar */}
      <div style={{
        width: sidebarOpen ? '280px' : '80px',
        backgroundColor: 'white',
        borderRight: '1px solid #e0e0e0',
        transition: 'width 0.3s ease',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Logo */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {sidebarOpen && (
            <h3 style={{ color: '#AF0003', margin: 0 }}>🕉️ Ganesh Darshan</h3>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'rgba(175, 0, 3, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
            }}
            title={sidebarOpen ? 'Collapse Sidebar' : 'Expand Sidebar'}
          >
            {sidebarOpen ? (
              <span style={{ color: '#AF0003', fontWeight: 'bold' }}>✕</span>
            ) : (
              <span style={{ color: '#AF0003', fontWeight: 'bold' }}>☰</span>
            )}
          </button>
        </div>

        {/* Menu Items */}
        <div style={{ flex: 1, padding: '20px 10px' }}>
          {menuItems.map((item) => (
            <div
              key={item.id}
              onClick={() => setCurrentPage(item.id)}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px 16px',
                marginBottom: '8px',
                borderRadius: '8px',
                cursor: 'pointer',
                backgroundColor: currentPage === item.id ? '#AF0003' : 'transparent',
                color: currentPage === item.id ? 'white' : '#666',
                transition: 'all 0.2s ease'
              }}
            >
              <span style={{ marginRight: sidebarOpen ? '12px' : '0', fontSize: '18px' }}>
                {item.icon}
              </span>
              {sidebarOpen && <span>{item.label}</span>}
            </div>
          ))}
        </div>

        {/* User Info */}
        <div style={{
          padding: '20px',
          borderTop: '1px solid #e0e0e0'
        }}>
          {sidebarOpen && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{ fontWeight: 'bold', color: '#232323' }}>{user?.username}</div>
              <div style={{ fontSize: '14px', color: '#666', textTransform: 'capitalize' }}>
                {user?.role}
              </div>
            </div>
          )}
          <button
            onClick={handleLogout}
            style={{
              width: '100%',
              padding: '8px 16px',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            {sidebarOpen ? 'Logout' : '🚪'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: '30px'
      }}>
        {renderContent()}
      </div>
    </div>
  );
};

export default SimpleDashboard;
