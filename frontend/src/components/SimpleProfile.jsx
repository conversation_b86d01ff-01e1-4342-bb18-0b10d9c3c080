import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const SimpleProfile = ({ onBack }) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    phone: user?.phone || '',
    profilePhoto: null,
    coverPhoto: null,
    existingProfilePhoto: null,
    existingCoverPhoto: null
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfileData(prev => ({
          ...prev,
          username: data.profile.username || '',
          email: data.profile.email || '',
          phone: data.profile.phone || '',
          existingProfilePhoto: data.profile.profileImage || null,
          existingCoverPhoto: data.profile.coverPhoto || null,
          profilePhoto: null,
          coverPhoto: null
        }));
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const handleProfileChange = (e) => {
    const { name, value, files } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: files ? files[0] : value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const formData = new FormData();
      
      formData.append('username', profileData.username);
      formData.append('email', profileData.email);
      formData.append('phone', profileData.phone);
      
      if (profileData.profilePhoto) {
        formData.append('profilePhoto', profileData.profilePhoto);
      }

      if (profileData.coverPhoto) {
        formData.append('coverPhoto', profileData.coverPhoto);
      }

      const response = await fetch('http://localhost:5001/api/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Profile updated successfully!');

        // Update profile data with new photos
        setProfileData(prev => ({
          ...prev,
          profilePhoto: null,
          coverPhoto: null,
          existingProfilePhoto: data.profile.profileImage || prev.existingProfilePhoto,
          existingCoverPhoto: data.profile.coverPhoto || prev.existingCoverPhoto
        }));

        // Update user data in localStorage
        const updatedUser = { ...user, ...data.profile };
        localStorage.setItem('user', JSON.stringify(updatedUser));

        // Refresh profile data
        fetchProfile();
      } else {
        setError(data.message || 'Failed to update profile');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('New passwords do not match');
      setLoading(false);
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setError('New password must be at least 6 characters long');
      setLoading(false);
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/profile/change-password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Password changed successfully!');
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setError(data.message || 'Failed to change password');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const inputStyle = {
    width: '100%',
    padding: '12px 16px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '16px',
    marginBottom: '16px',
    boxSizing: 'border-box'
  };

  const tabStyle = (isActive) => ({
    padding: '12px 24px',
    border: 'none',
    backgroundColor: isActive ? '#AF0003' : 'transparent',
    color: isActive ? 'white' : '#666',
    borderRadius: '8px 8px 0 0',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: isActive ? 'bold' : 'normal'
  });

  return (
    <div style={{ fontFamily: 'Arial, sans-serif' }}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '30px' }}>
        <button
          onClick={onBack}
          style={{
            backgroundColor: 'transparent',
            border: '1px solid #e0e0e0',
            padding: '8px 16px',
            borderRadius: '8px',
            cursor: 'pointer',
            marginRight: '20px'
          }}
        >
          ← Back
        </button>
        <h2 style={{ color: '#AF0003', margin: 0 }}>Profile Management</h2>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '12px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {success && (
        <div style={{
          backgroundColor: '#f0fdf4',
          color: '#166534',
          padding: '12px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #bbf7d0'
        }}>
          {success}
        </div>
      )}

      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {/* Tabs */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#f9fafb'
        }}>
          <button
            onClick={() => setActiveTab('profile')}
            style={tabStyle(activeTab === 'profile')}
          >
            👤 Profile Info
          </button>
          <button
            onClick={() => setActiveTab('password')}
            style={tabStyle(activeTab === 'password')}
          >
            🔒 Change Password
          </button>
        </div>

        {/* Tab Content */}
        <div style={{ padding: '30px' }}>
          {activeTab === 'profile' && (
            <div>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Profile Information</h3>
              
              <form onSubmit={handleProfileSubmit}>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Username *
                    </label>
                    <input
                      type="text"
                      name="username"
                      value={profileData.username}
                      onChange={handleProfileChange}
                      style={inputStyle}
                      required
                      placeholder="Enter username"
                    />
                    <small style={{ color: '#666', fontSize: '12px' }}>
                      This will be your profile URL: /profile/{profileData.username}
                    </small>
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleProfileChange}
                      style={inputStyle}
                      required
                      placeholder="Enter email"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleProfileChange}
                      style={inputStyle}
                      required
                      placeholder="Enter 10-digit phone number"
                      pattern="[6-9][0-9]{9}"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Profile Photo
                    </label>
                    <input
                      type="file"
                      name="profilePhoto"
                      onChange={handleProfileChange}
                      style={inputStyle}
                      accept="image/*"
                    />
                    <small style={{ color: '#666', fontSize: '12px' }}>
                      Recommended: Square image, max 2MB
                    </small>
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Cover Photo
                    </label>
                    <input
                      type="file"
                      name="coverPhoto"
                      onChange={handleProfileChange}
                      style={inputStyle}
                      accept="image/*"
                    />
                    <small style={{ color: '#666', fontSize: '12px' }}>
                      Recommended: 1200x400px, max 5MB
                    </small>
                  </div>
                </div>

                {/* Photo Preview Section */}
                <div style={{
                  marginTop: '30px',
                  padding: '20px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e0e0e0'
                }}>
                  <h4 style={{ color: '#232323', marginBottom: '20px' }}>Photo Preview</h4>

                  {/* Cover Photo Preview */}
                  <div style={{ marginBottom: '20px' }}>
                    <div style={{
                      width: '100%',
                      height: '200px',
                      backgroundColor: '#e5e7eb',
                      borderRadius: '12px',
                      overflow: 'hidden',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {profileData.coverPhoto ? (
                        <img
                          src={URL.createObjectURL(profileData.coverPhoto)}
                          alt="Cover Preview"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                        />
                      ) : profileData.existingCoverPhoto ? (
                        <img
                          src={`http://localhost:5001${profileData.existingCoverPhoto}`}
                          alt="Current Cover Photo"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                        />
                      ) : (
                        <div style={{ textAlign: 'center', color: '#6b7280' }}>
                          <div style={{ fontSize: '48px', marginBottom: '8px' }}>🖼️</div>
                          <div>Cover Photo Preview</div>
                          <div style={{ fontSize: '12px', marginTop: '4px' }}>1200 x 400 recommended</div>
                        </div>
                      )}

                      {/* Profile Photo Overlay */}
                      <div style={{
                        position: 'absolute',
                        bottom: '20px',
                        left: '20px',
                        width: '100px',
                        height: '100px',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        border: '4px solid white',
                        backgroundColor: '#f3f4f6'
                      }}>
                        {profileData.profilePhoto ? (
                          <img
                            src={URL.createObjectURL(profileData.profilePhoto)}
                            alt="Profile Preview"
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        ) : profileData.existingProfilePhoto ? (
                          <img
                            src={`http://localhost:5001${profileData.existingProfilePhoto}`}
                            alt="Current Profile Photo"
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        ) : (
                          <div style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#6b7280',
                            fontSize: '24px'
                          }}>
                            👤
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div style={{ marginTop: '30px' }}>
                  <button
                    type="submit"
                    disabled={loading}
                    style={{
                      backgroundColor: '#AF0003',
                      color: 'white',
                      padding: '12px 24px',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      cursor: loading ? 'not-allowed' : 'pointer',
                      opacity: loading ? 0.6 : 1
                    }}
                  >
                    {loading ? 'Updating...' : 'Update Profile'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {activeTab === 'password' && (
            <div>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Change Password</h3>
              
              <form onSubmit={handlePasswordSubmit}>
                <div style={{ maxWidth: '400px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Current Password *
                    </label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={passwordData.currentPassword}
                      onChange={handlePasswordChange}
                      style={inputStyle}
                      required
                      placeholder="Enter current password"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      New Password *
                    </label>
                    <input
                      type="password"
                      name="newPassword"
                      value={passwordData.newPassword}
                      onChange={handlePasswordChange}
                      style={inputStyle}
                      required
                      placeholder="Enter new password"
                      minLength="6"
                    />
                    <small style={{ color: '#666', fontSize: '12px' }}>
                      Password must be at least 6 characters long
                    </small>
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Confirm New Password *
                    </label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordChange}
                      style={inputStyle}
                      required
                      placeholder="Confirm new password"
                    />
                  </div>

                  <div style={{ marginTop: '30px' }}>
                    <button
                      type="submit"
                      disabled={loading}
                      style={{
                        backgroundColor: '#AF0003',
                        color: 'white',
                        padding: '12px 24px',
                        border: 'none',
                        borderRadius: '8px',
                        fontSize: '16px',
                        cursor: loading ? 'not-allowed' : 'pointer',
                        opacity: loading ? 0.6 : 1
                      }}
                    >
                      {loading ? 'Changing...' : 'Change Password'}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>

      {/* Account Info */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        marginTop: '20px'
      }}>
        <h3 style={{ color: '#232323', marginBottom: '16px' }}>Account Information</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
          <div>
            <strong>Role:</strong> {user?.role?.replace('_', ' ').toUpperCase()}
          </div>
          <div>
            <strong>Category:</strong> {user?.category ? user.category.replace('_', ' ') : 'Not selected'}
          </div>
          <div>
            <strong>Member Since:</strong> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
          </div>
          <div>
            <strong>Status:</strong> 
            <span style={{
              marginLeft: '8px',
              padding: '2px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              backgroundColor: user?.isActive ? '#10b981' : '#dc2626',
              color: 'white'
            }}>
              {user?.isActive ? 'ACTIVE' : 'INACTIVE'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleProfile;
