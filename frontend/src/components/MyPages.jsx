import React, { useState, useEffect } from 'react';
import ActionButtons from './ActionButtons';

const MyPages = () => {
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [counts, setCounts] = useState({ draft: 0, published: 0, total: 0 });

  useEffect(() => {
    fetchMyListings();
  }, [activeTab]);

  const fetchMyListings = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const status = activeTab === 'all' ? 'all' : activeTab;
      const response = await fetch(`http://localhost:5001/api/listings/my-listings?status=${status}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setListings(data.listings);
        setCounts(data.counts);
      } else {
        setError('Failed to fetch your pages');
      }
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const handlePublish = async (listingId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/listings/${listingId}/publish`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Page published successfully!');
        fetchMyListings();
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to publish page');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
  };

  const handleUnpublish = async (listingId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/listings/${listingId}/unpublish`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Page moved to draft successfully!');
        fetchMyListings();
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to unpublish page');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
  };

  const handleDelete = async (listingId) => {
    if (!window.confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/listings/${listingId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Page deleted successfully!');
        fetchMyListings();
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete page');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { color: '#6b7280', icon: '📝', text: 'Draft' },
      published: { color: '#10b981', icon: '🌐', text: 'Published' },
      pending: { color: '#f59e0b', icon: '⏳', text: 'Pending' },
      rejected: { color: '#dc2626', icon: '❌', text: 'Rejected' }
    };

    const config = statusConfig[status] || statusConfig.draft;
    
    return (
      <span style={{
        backgroundColor: config.color,
        color: 'white',
        padding: '4px 12px',
        borderRadius: '16px',
        fontSize: '12px',
        fontWeight: 'bold'
      }}>
        {config.icon} {config.text}
      </span>
    );
  };

  const getListingImage = (listing) => {
    if (listing.images && listing.images.length > 0) {
      const primaryImage = listing.images.find(img => img.isPrimary) || listing.images[0];
      return `http://localhost:5001${primaryImage.url}`;
    }
    return null;
  };

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <div>
          <h2 style={{ color: '#232323', margin: '0 0 8px 0', fontSize: '28px' }}>
            📄 My Pages
          </h2>
          <p style={{ color: '#666', margin: 0 }}>
            Manage your Ganesha celebration pages
          </p>
        </div>
        
        <button
          onClick={() => window.location.href = '/create-listing'}
          style={{
            backgroundColor: '#AF0003',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}
        >
          ➕ Create New Page
        </button>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div style={{
          backgroundColor: '#f0f9ff',
          color: '#0369a1',
          padding: '16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #bae6fd'
        }}>
          {success}
        </div>
      )}

      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {/* Tabs */}
      <div style={{ 
        display: 'flex',
        borderBottom: '2px solid #e5e7eb',
        marginBottom: '30px'
      }}>
        {[
          { key: 'all', label: `All (${counts.total})` },
          { key: 'draft', label: `Drafts (${counts.draft})` },
          { key: 'published', label: `Published (${counts.published})` }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              borderBottom: activeTab === tab.key ? '3px solid #AF0003' : '3px solid transparent',
              padding: '16px 24px',
              fontSize: '16px',
              fontWeight: activeTab === tab.key ? 'bold' : 'normal',
              color: activeTab === tab.key ? '#AF0003' : '#666',
              cursor: 'pointer'
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Loading State */}
      {loading ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔄</div>
          <p style={{ color: '#666', fontSize: '18px' }}>Loading your pages...</p>
        </div>
      ) : listings.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>📄</div>
          <h3 style={{ color: '#232323', marginBottom: '10px' }}>No pages found</h3>
          <p style={{ color: '#666', marginBottom: '20px' }}>
            {activeTab === 'draft' ? 'You have no draft pages.' : 
             activeTab === 'published' ? 'You have no published pages.' : 
             'You haven\'t created any pages yet.'}
          </p>
          <button
            onClick={() => window.location.href = '/create-listing'}
            style={{
              backgroundColor: '#AF0003',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            Create Your First Page
          </button>
        </div>
      ) : (
        /* Pages Grid */
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))', 
          gap: '25px'
        }}>
          {listings.map((listing) => (
            <div
              key={listing._id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                overflow: 'hidden',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                position: 'relative'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                e.currentTarget.querySelector('.page-actions').style.opacity = '1';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                e.currentTarget.querySelector('.page-actions').style.opacity = '0';
              }}
            >
              {/* Image */}
              <div style={{ position: 'relative', height: '200px', overflow: 'hidden' }}>
                {getListingImage(listing) ? (
                  <img
                    src={getListingImage(listing)}
                    alt={listing.title}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <div style={{
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '48px',
                    color: '#AF0003'
                  }}>
                    🕉️
                  </div>
                )}
                
                {/* Status Badge */}
                <div style={{
                  position: 'absolute',
                  top: '12px',
                  left: '12px'
                }}>
                  {getStatusBadge(listing.status)}
                </div>

                {/* Category Badge */}
                <div style={{
                  position: 'absolute',
                  top: '12px',
                  right: '12px',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '6px 12px',
                  borderRadius: '16px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  {listing.category}
                </div>

                {/* Action Buttons */}
                <div 
                  className="page-actions"
                  style={{
                    position: 'absolute',
                    bottom: '12px',
                    right: '12px',
                    display: 'flex',
                    gap: '8px',
                    opacity: '0',
                    transition: 'opacity 0.2s ease'
                  }}
                >
                  {listing.status === 'draft' ? (
                    <button
                      onClick={() => handlePublish(listing._id)}
                      style={{
                        backgroundColor: 'rgba(16, 185, 129, 0.9)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 12px',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}
                      title="Publish Page"
                    >
                      🌐 Publish
                    </button>
                  ) : (
                    <button
                      onClick={() => handleUnpublish(listing._id)}
                      style={{
                        backgroundColor: 'rgba(107, 114, 128, 0.9)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 12px',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}
                      title="Move to Draft"
                    >
                      📝 Draft
                    </button>
                  )}
                  
                  <ActionButtons
                    onEdit={() => window.location.href = `/edit-listing/${listing._id}`}
                    onDelete={() => handleDelete(listing._id)}
                    editTitle="Edit Page"
                    deleteTitle="Delete Page"
                    size="small"
                  />
                </div>
              </div>

              {/* Content */}
              <div style={{ padding: '20px' }}>
                <h3 style={{ 
                  color: '#232323', 
                  margin: '0 0 8px 0',
                  fontSize: '18px',
                  fontWeight: 'bold',
                  lineHeight: '1.3'
                }}>
                  {listing.title}
                </h3>
                
                <p style={{ 
                  color: '#666', 
                  margin: '0 0 12px 0',
                  fontSize: '14px',
                  lineHeight: '1.4',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {listing.description}
                </p>

                {/* Location */}
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  marginBottom: '12px'
                }}>
                  <span style={{ marginRight: '6px' }}>📍</span>
                  <span style={{ 
                    color: '#666', 
                    fontSize: '14px'
                  }}>
                    {listing.location.city}, {listing.location.district}
                  </span>
                </div>

                {/* Stats and Actions */}
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  paddingTop: '12px',
                  borderTop: '1px solid #f0f0f0'
                }}>
                  <div style={{ 
                    display: 'flex', 
                    gap: '12px',
                    fontSize: '12px',
                    color: '#666'
                  }}>
                    <span>👁️ {listing.views || 0}</span>
                    <span>📅 {new Date(listing.createdAt).toLocaleDateString()}</span>
                  </div>
                  
                  {listing.status === 'published' && (
                    <button
                      onClick={() => window.location.href = `/page/${listing._id}`}
                      style={{
                        backgroundColor: '#AF0003',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '6px 12px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      👁️ View Page
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MyPages;
