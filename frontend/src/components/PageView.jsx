import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

const PageView = () => {
  const { username } = useParams();
  const [listing, setListing] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('about');

  useEffect(() => {
    fetchPage();
  }, [username]);

  const fetchPage = async () => {
    try {
      const response = await fetch(`http://localhost:5001/api/user-page/${username}`);
      if (response.ok) {
        const data = await response.json();
        setListing(data.page);
      } else {
        setError('Page not found');
      }
    } catch (error) {
      console.error('Error fetching page:', error);
      setError('Failed to load page');
    }
    setLoading(false);
  };

  const getCoverImage = () => {
    if (listing?.user?.coverPhoto) {
      return `http://localhost:5001${listing.user.coverPhoto}`;
    }
    if (listing?.gallery?.albums && Object.keys(listing.gallery.albums).length > 0) {
      const firstAlbum = Object.values(listing.gallery.albums)[0];
      if (firstAlbum.length > 0) {
        return `http://localhost:5001${firstAlbum[0].url}`;
      }
    }
    return null;
  };

  const getProfileImage = () => {
    if (listing?.user?.profileImage) {
      return `http://localhost:5001${listing.user.profileImage}`;
    }
    return null;
  };

  const getCategoryLabel = (category) => {
    const categoryMap = {
      'mandal_ganesha': 'Mandal',
      'home_ganesha': 'Home',
      'celebrity_ganesha': 'Celebrity'
    };
    return categoryMap[category] || category;
  };

  const getLocationString = () => {
    if (listing?.mandalInfo?.address) {
      const addr = listing.mandalInfo.address;
      return `${addr.city}, ${addr.state}`;
    }
    return 'Location not specified';
  };

  const formatCelebrationStatus = () => {
    if (!listing?.ganeshaDetails?.celebrationDuration) return null;

    const { startDate, endDate } = listing.ganeshaDetails.celebrationDuration;
    if (!startDate || !endDate) return null;

    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (now < start) {
      return { text: 'Upcoming', color: '#f59e0b', icon: '⏳' };
    } else if (now > end) {
      return { text: 'Completed', color: '#6b7280', icon: '✅' };
    } else {
      return { text: 'Ongoing', color: '#10b981', icon: '🎉' };
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>🕉️</div>
          <p style={{ color: '#666', fontSize: '18px' }}>Loading page...</p>
        </div>
      </div>
    );
  }

  if (error || !listing) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>❌</div>
          <h2 style={{ color: '#232323', marginBottom: '10px' }}>Page Not Found</h2>
          <p style={{ color: '#666' }}>{error}</p>
          <button
            onClick={() => window.history.back()}
            style={{
              backgroundColor: '#AF0003',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const celebrationStatus = formatCelebrationStatus();

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc'
    }}>
      {/* Add CSS animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        @keyframes slideInLeft {
          from { transform: translateX(-30px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideInRight {
          from { transform: translateX(30px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes fadeInUp {
          from { transform: translateY(30px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
        @media (max-width: 768px) {
          .tab-label { display: none; }
        }
      `}</style>
      {/* Hero Section with Floating Elements */}
      <div style={{
        position: 'relative',
        background: 'linear-gradient(135deg, #AF0003 0%, #F2A71B 100%)',
        overflow: 'hidden'
      }}>
        {/* Animated Background Pattern */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: getCoverImage() ? `url(${getCoverImage()})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: getCoverImage() ? 0.3 : 0
        }} />

        {/* Floating Decorative Elements */}
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          fontSize: '24px',
          opacity: 0.2,
          animation: 'float 3s ease-in-out infinite'
        }}>🕉️</div>
        <div style={{
          position: 'absolute',
          bottom: '30px',
          left: '30px',
          fontSize: '18px',
          opacity: 0.2,
          animation: 'float 4s ease-in-out infinite reverse'
        }}>🪔</div>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '60px 20px',
          position: 'relative',
          zIndex: 2
        }}>

          {/* Profile Card with Glassmorphism */}
          <div style={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255,255,255,0.2)',
            transform: 'translateY(0)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 30px 60px rgba(0,0,0,0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
          }}>

            <div style={{ display: 'flex', alignItems: 'center', gap: '30px', marginBottom: '30px' }}>
              {/* Animated Profile Image */}
              <div style={{
                position: 'relative',
                width: '120px',
                height: '120px'
              }}>
                <div style={{
                  width: '120px',
                  height: '120px',
                  borderRadius: '50%',
                  background: 'linear-gradient(45deg, #AF0003, #F2A71B)',
                  padding: '4px',
                  animation: 'rotate 10s linear infinite'
                }}>
                  <div style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    backgroundColor: '#f3f4f6',
                    backgroundImage: getProfileImage() ? `url(${getProfileImage()})` : 'none',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '36px',
                    color: '#AF0003',
                    transition: 'transform 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}>
                    {!getProfileImage() && '🕉️'}
                  </div>
                </div>

                {/* Status Indicator */}
                <div style={{
                  position: 'absolute',
                  bottom: '5px',
                  right: '5px',
                  width: '24px',
                  height: '24px',
                  backgroundColor: '#10b981',
                  borderRadius: '50%',
                  border: '3px solid white',
                  animation: 'pulse 2s infinite'
                }} />
              </div>

              {/* Page Info */}
              <div style={{ flex: 1 }}>
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '12px',
                  backgroundColor: '#AF0003',
                  color: 'white',
                  padding: '6px 16px',
                  borderRadius: '20px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  marginBottom: '12px',
                  animation: 'slideInRight 0.6s ease'
                }}>
                  <span>✨</span>
                  {getCategoryLabel(listing.user?.category)}
                </div>

                <h1 style={{
                  color: '#232323',
                  fontSize: '32px',
                  fontWeight: 'bold',
                  margin: '0 0 12px 0',
                  animation: 'slideInLeft 0.6s ease'
                }}>
                  {listing.mandalInfo?.mandalName || listing.user?.username}
                </h1>

                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '16px',
                  marginBottom: '20px',
                  animation: 'fadeInUp 0.8s ease'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    color: '#666',
                    fontSize: '14px',
                    backgroundColor: '#f8fafc',
                    padding: '8px 12px',
                    borderRadius: '12px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#e5e7eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}>
                    <span>📍</span>
                    {getLocationString()}
                  </div>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    color: '#666',
                    fontSize: '14px',
                    backgroundColor: '#f8fafc',
                    padding: '8px 12px',
                    borderRadius: '12px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#e5e7eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}>
                    <span>👁️</span>
                    {listing.user?.pageViews || 0} views
                  </div>
                </div>

                {/* Action Buttons with Hover Effects */}
                <div style={{ display: 'flex', gap: '12px', animation: 'fadeInUp 1s ease' }}>
                  <button style={{
                    background: 'linear-gradient(45deg, #AF0003, #F2A71B)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    padding: '12px 24px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 15px rgba(175, 0, 3, 0.3)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-3px) scale(1.05)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(175, 0, 3, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = '0 4px 15px rgba(175, 0, 3, 0.3)';
                  }}>
                    ❤️ Follow
                  </button>

                  <button style={{
                    backgroundColor: 'white',
                    color: '#232323',
                    border: '2px solid #e5e7eb',
                    borderRadius: '12px',
                    padding: '12px 24px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#AF0003';
                    e.currentTarget.style.color = '#AF0003';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#232323';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}>
                    📞 Contact
                  </button>

                  <button style={{
                    backgroundColor: 'white',
                    color: '#232323',
                    border: '2px solid #e5e7eb',
                    borderRadius: '12px',
                    padding: '12px 24px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#F2A71B';
                    e.currentTarget.style.color = '#F2A71B';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#232323';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}>
                    📍 Directions
                  </button>
                </div>
              </div>
            </div>

            {/* Quick Stats Cards */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
              gap: '16px',
              animation: 'fadeInUp 1.2s ease'
            }}>
              {[
                { icon: '📸', label: 'Photos', value: listing.stats?.totalPhotos || 0, color: '#10b981' },
                { icon: '🎥', label: 'Videos', value: listing.stats?.totalVideos || 0, color: '#3b82f6' },
                { icon: '👥', label: 'Team', value: listing.stats?.teamMembers || 0, color: '#8b5cf6' },
                { icon: '⭐', label: 'Rating', value: '4.8', color: '#f59e0b' }
              ].map((stat, index) => (
                <div key={index} style={{
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  textAlign: 'center',
                  border: '1px solid #f0f0f0',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
                  e.currentTarget.style.borderColor = stat.color;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.borderColor = '#f0f0f0';
                }}>
                  <div style={{ fontSize: '24px', marginBottom: '8px' }}>{stat.icon}</div>
                  <div style={{ fontSize: '20px', fontWeight: 'bold', color: stat.color, marginBottom: '4px' }}>
                    {stat.value}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Floating Navigation */}
      <div style={{
        position: 'sticky',
        top: '20px',
        zIndex: 100,
        margin: '20px auto',
        maxWidth: '600px',
        padding: '0 20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '50px',
          padding: '8px',
          boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
          border: '1px solid rgba(0,0,0,0.05)',
          backdropFilter: 'blur(10px)'
        }}>
          <div style={{ display: 'flex', gap: '4px' }}>
            {[
              { id: 'about', label: 'About', icon: '📖' },
              { id: 'photos', label: 'Photos', icon: '📸' },
              { id: 'videos', label: 'Videos', icon: '🎥' },
              { id: 'reviews', label: 'Reviews', icon: '⭐' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  flex: 1,
                  backgroundColor: activeTab === tab.id ? '#AF0003' : 'transparent',
                  color: activeTab === tab.id ? 'white' : '#666',
                  border: 'none',
                  borderRadius: '40px',
                  padding: '12px 16px',
                  fontSize: '14px',
                  fontWeight: activeTab === tab.id ? 'bold' : 'normal',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '6px'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.color = '#AF0003';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#666';
                  }
                }}
              >
                <span>{tab.icon}</span>
                <span className="tab-label">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '20px',
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '30px'
      }}>
        {/* Main Content */}
        <div>
          {activeTab === 'about' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '20px',
              padding: '32px',
              boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
              marginBottom: '24px',
              border: '1px solid rgba(0,0,0,0.05)',
              animation: 'fadeInUp 0.6s ease',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 12px 40px rgba(0,0,0,0.12)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.08)';
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '12px',
                  background: 'linear-gradient(45deg, #AF0003, #F2A71B)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '18px'
                }}>📖</div>
                <h3 style={{ color: '#232323', margin: 0, fontSize: '24px', fontWeight: 'bold' }}>About</h3>
              </div>
              <p style={{ color: '#666', lineHeight: '1.7', marginBottom: '24px', fontSize: '16px' }}>
                {listing.mandalInfo?.description || 'Welcome to our Ganesha celebration page.'}
              </p>

              {/* Ganesha Details */}
              {listing.ganeshaDetails && (
                <div style={{
                  marginBottom: '24px',
                  backgroundColor: '#f8fafc',
                  borderRadius: '16px',
                  padding: '24px',
                  border: '1px solid #e5e7eb'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '8px',
                      backgroundColor: '#AF0003',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px'
                    }}>🕉️</div>
                    <h4 style={{ color: '#232323', margin: 0, fontSize: '18px', fontWeight: 'bold' }}>Ganesha Details</h4>
                  </div>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                    {listing.ganeshaDetails.murtiDetails?.dimensions?.height && (
                      <div style={{
                        backgroundColor: 'white',
                        padding: '16px',
                        borderRadius: '12px',
                        border: '1px solid #e5e7eb',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#AF0003';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>HEIGHT</div>
                        <div style={{ fontWeight: 'bold', color: '#232323' }}>
                          {listing.ganeshaDetails.murtiDetails.dimensions.height} {listing.ganeshaDetails.murtiDetails.dimensions.unit}
                        </div>
                      </div>
                    )}
                    {listing.ganeshaDetails.decoration?.type && (
                      <div style={{
                        backgroundColor: 'white',
                        padding: '16px',
                        borderRadius: '12px',
                        border: '1px solid #e5e7eb',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#F2A71B';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>DECORATION</div>
                        <div style={{ fontWeight: 'bold', color: '#232323' }}>
                          {listing.ganeshaDetails.decoration.type}
                        </div>
                      </div>
                    )}
                    {listing.ganeshaDetails.murtiDetails?.material && (
                      <div style={{
                        backgroundColor: 'white',
                        padding: '16px',
                        borderRadius: '12px',
                        border: '1px solid #e5e7eb',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#10b981';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>MATERIAL</div>
                        <div style={{ fontWeight: 'bold', color: '#232323' }}>
                          {listing.ganeshaDetails.murtiDetails.material}
                        </div>
                      </div>
                    )}
                    {listing.ganeshaDetails.murtiDetails?.murtikar && (
                      <div style={{
                        backgroundColor: 'white',
                        padding: '16px',
                        borderRadius: '12px',
                        border: '1px solid #e5e7eb',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#8b5cf6';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>MURTIKAR</div>
                        <div style={{ fontWeight: 'bold', color: '#232323' }}>
                          {listing.ganeshaDetails.murtiDetails.murtikar}
                        </div>
                      </div>
                    )}
                  </div>
                  {listing.ganeshaDetails.murtiDetails?.material === 'Eco Friendly' && (
                    <div style={{
                      marginTop: '16px',
                      padding: '16px',
                      backgroundColor: '#dcfce7',
                      borderRadius: '12px',
                      color: '#166534',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      border: '2px solid #bbf7d0'
                    }}>
                      🌱 Eco-Friendly Celebration
                    </div>
                  )}
                </div>
              )}

              {/* Facilities */}
              {listing.ganeshaDetails?.facilities && (
                <div>
                  <h4 style={{ color: '#232323', marginBottom: '12px' }}>Facilities</h4>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {listing.ganeshaDetails.facilities.wheelchairAccessible && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        ♿ Wheelchair Accessible
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.parking && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🚗 Parking Available
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.restrooms && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🚻 Restrooms
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.waterFacility && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        💧 Water Facility
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.prasadDistribution && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🍽️ Prasad Distribution
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.liveAarti && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🎭 Live Aarti
                      </span>
                    )}
                    {listing.ganeshaDetails.facilities.onlineAarti && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        📱 Online Aarti
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'photos' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Photos</h3>
              {listing.gallery?.albums && Object.keys(listing.gallery.albums).length > 0 ? (
                <div>
                  {Object.entries(listing.gallery.albums).map(([albumName, photos]) => (
                    <div key={albumName} style={{ marginBottom: '24px' }}>
                      <h4 style={{ color: '#666', marginBottom: '12px', fontSize: '16px' }}>
                        {albumName} ({photos.length} photos)
                      </h4>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                        gap: '12px'
                      }}>
                        {photos.map((photo, index) => (
                          <div key={index} style={{
                            aspectRatio: '1',
                            borderRadius: '8px',
                            overflow: 'hidden',
                            backgroundColor: '#f3f4f6',
                            position: 'relative'
                          }}>
                            <img
                              src={`http://localhost:5001${photo.url}`}
                              alt={photo.title || photo.caption || `Photo ${index + 1}`}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                                cursor: 'pointer'
                              }}
                            />
                            {photo.title && (
                              <div style={{
                                position: 'absolute',
                                bottom: '0',
                                left: '0',
                                right: '0',
                                backgroundColor: 'rgba(0,0,0,0.7)',
                                color: 'white',
                                padding: '8px',
                                fontSize: '12px'
                              }}>
                                {photo.title}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                  No photos available
                </p>
              )}
            </div>
          )}

          {activeTab === 'videos' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Videos</h3>
              <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                Videos coming soon...
              </p>
            </div>
          )}

          {activeTab === 'reviews' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Reviews</h3>
              <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                No reviews yet. Be the first to review!
              </p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div>
          {/* Contact Info */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            marginBottom: '20px'
          }}>
            <h4 style={{ color: '#232323', marginBottom: '16px' }}>Contact Information</h4>
            <div style={{ marginBottom: '12px' }}>
              <strong>📞 Phone:</strong><br />
              <a href={`tel:${listing.mandalInfo?.phone}`} style={{ color: '#AF0003', textDecoration: 'none' }}>
                {listing.mandalInfo?.phone}
              </a>
            </div>
            {listing.mandalInfo?.email && (
              <div style={{ marginBottom: '12px' }}>
                <strong>✉️ Email:</strong><br />
                <a href={`mailto:${listing.mandalInfo.email}`} style={{ color: '#AF0003', textDecoration: 'none' }}>
                  {listing.mandalInfo.email}
                </a>
              </div>
            )}
            {listing.mandalInfo?.address && (
              <div>
                <strong>📍 Address:</strong><br />
                <span style={{ color: '#666' }}>{listing.mandalInfo.address.street}</span><br />
                <span style={{ color: '#666' }}>
                  {listing.mandalInfo.address.city}, {listing.mandalInfo.address.state}<br />
                  {listing.mandalInfo.address.country} - {listing.mandalInfo.address.pincode}
                </span>
              </div>
            )}
          </div>

          {/* Visiting Hours */}
          {listing.visitingHours && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '20px'
            }}>
              <h4 style={{ color: '#232323', marginBottom: '16px' }}>Visiting Hours</h4>
              {listing.visitingHours.isOpen24Hours ? (
                <p style={{ color: '#10b981', fontWeight: 'bold' }}>🕐 Open 24 Hours</p>
              ) : (
                <p style={{ color: '#666' }}>
                  🕐 {listing.visitingHours.openTime} - {listing.visitingHours.closeTime}
                </p>
              )}
            </div>
          )}

          {/* Quick Stats */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h4 style={{ color: '#232323', marginBottom: '16px' }}>Page Stats</h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>👁️ Views:</span>
                <strong>{listing.user?.pageViews || 0}</strong>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>📅 Created:</span>
                <strong>{new Date(listing.user?.createdAt).toLocaleDateString()}</strong>
              </div>
              {listing.user?.publishedAt && (
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>🌐 Published:</span>
                  <strong>{new Date(listing.user.publishedAt).toLocaleDateString()}</strong>
                </div>
              )}
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>📸 Photos:</span>
                <strong>{listing.stats?.totalPhotos || 0}</strong>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>🎥 Videos:</span>
                <strong>{listing.stats?.totalVideos || 0}</strong>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>👥 Team:</span>
                <strong>{listing.stats?.teamMembers || 0}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageView;
