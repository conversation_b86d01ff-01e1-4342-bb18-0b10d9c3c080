import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

const PageView = () => {
  const { id } = useParams();
  const [listing, setListing] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('about');

  useEffect(() => {
    fetchListing();
  }, [id]);

  const fetchListing = async () => {
    try {
      const response = await fetch(`http://localhost:5001/api/listings/${id}`);
      if (response.ok) {
        const data = await response.json();
        setListing(data.listing);
      } else {
        setError('Listing not found');
      }
    } catch (error) {
      console.error('Error fetching listing:', error);
      setError('Failed to load listing');
    }
    setLoading(false);
  };

  const getCoverImage = () => {
    if (listing?.images && listing.images.length > 0) {
      const coverImage = listing.images.find(img => img.isPrimary) || listing.images[0];
      return `http://localhost:5001${coverImage.url}`;
    }
    return null;
  };

  const getProfileImage = () => {
    if (listing?.images && listing.images.length > 1) {
      return `http://localhost:5001${listing.images[1].url}`;
    }
    return getCoverImage();
  };

  const formatCelebrationStatus = () => {
    if (!listing?.ganeshaDetails?.celebrationDuration) return null;
    
    const { startDate, endDate } = listing.ganeshaDetails.celebrationDuration;
    if (!startDate || !endDate) return null;

    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (now < start) {
      return { text: 'Upcoming', color: '#f59e0b', icon: '⏳' };
    } else if (now > end) {
      return { text: 'Completed', color: '#6b7280', icon: '✅' };
    } else {
      return { text: 'Ongoing', color: '#10b981', icon: '🎉' };
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>🕉️</div>
          <p style={{ color: '#666', fontSize: '18px' }}>Loading page...</p>
        </div>
      </div>
    );
  }

  if (error || !listing) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>❌</div>
          <h2 style={{ color: '#232323', marginBottom: '10px' }}>Page Not Found</h2>
          <p style={{ color: '#666' }}>{error}</p>
          <button
            onClick={() => window.history.back()}
            style={{
              backgroundColor: '#AF0003',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const celebrationStatus = formatCelebrationStatus();

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f0f2f5'
    }}>
      {/* Cover Photo Section */}
      <div style={{ 
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto',
          position: 'relative'
        }}>
          {/* Cover Image */}
          <div style={{
            height: '400px',
            backgroundColor: '#e5e7eb',
            backgroundImage: getCoverImage() ? `url(${getCoverImage()})` : 'none',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative'
          }}>
            {!getCoverImage() && (
              <div style={{ fontSize: '72px', color: '#AF0003' }}>🕉️</div>
            )}
            
            {/* Category Badge */}
            <div style={{
              position: 'absolute',
              top: '20px',
              right: '20px',
              backgroundColor: 'rgba(0,0,0,0.7)',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '14px',
              fontWeight: 'bold'
            }}>
              {listing.category}
            </div>

            {/* Status Badge */}
            {celebrationStatus && (
              <div style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                backgroundColor: celebrationStatus.color,
                color: 'white',
                padding: '8px 16px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: 'bold'
              }}>
                {celebrationStatus.icon} {celebrationStatus.text}
              </div>
            )}
          </div>

          {/* Profile Section */}
          <div style={{ 
            padding: '20px',
            display: 'flex',
            alignItems: 'flex-end',
            gap: '20px',
            position: 'relative',
            marginTop: '-80px'
          }}>
            {/* Profile Image */}
            <div style={{
              width: '160px',
              height: '160px',
              borderRadius: '50%',
              border: '6px solid white',
              backgroundColor: '#f3f4f6',
              backgroundImage: getProfileImage() ? `url(${getProfileImage()})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '48px',
              color: '#AF0003',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
            }}>
              {!getProfileImage() && '🕉️'}
            </div>

            {/* Page Info */}
            <div style={{ flex: 1, paddingTop: '60px' }}>
              <h1 style={{ 
                color: '#232323', 
                fontSize: '32px', 
                fontWeight: 'bold',
                margin: '0 0 8px 0'
              }}>
                {listing.title}
              </h1>
              
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                gap: '20px',
                marginBottom: '16px'
              }}>
                <span style={{ color: '#666', fontSize: '16px' }}>
                  📍 {listing.location.city}, {listing.location.district}
                </span>
                <span style={{ color: '#666', fontSize: '16px' }}>
                  👁️ {listing.views || 0} views
                </span>
                {listing.ratings?.average > 0 && (
                  <span style={{ color: '#666', fontSize: '16px' }}>
                    ⭐ {listing.ratings.average.toFixed(1)} ({listing.ratings.count} reviews)
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <button style={{
                  backgroundColor: '#AF0003',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  ❤️ Follow
                </button>
                <button style={{
                  backgroundColor: '#e5e7eb',
                  color: '#374151',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  📞 Contact
                </button>
                <button style={{
                  backgroundColor: '#e5e7eb',
                  color: '#374151',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  📍 Directions
                </button>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div style={{ 
            borderTop: '1px solid #e5e7eb',
            padding: '0 20px'
          }}>
            <div style={{ 
              display: 'flex',
              gap: '0'
            }}>
              {['about', 'photos', 'videos', 'reviews'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderBottom: activeTab === tab ? '3px solid #AF0003' : '3px solid transparent',
                    padding: '16px 24px',
                    fontSize: '16px',
                    fontWeight: activeTab === tab ? 'bold' : 'normal',
                    color: activeTab === tab ? '#AF0003' : '#666',
                    cursor: 'pointer',
                    textTransform: 'capitalize'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto',
        padding: '20px',
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '20px'
      }}>
        {/* Main Content */}
        <div>
          {activeTab === 'about' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '20px'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>About</h3>
              <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>
                {listing.description}
              </p>

              {/* Ganesha Details */}
              {listing.ganeshaDetails && (
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#232323', marginBottom: '12px' }}>Ganesha Details</h4>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                    {listing.ganeshaDetails.height && (
                      <div>
                        <strong>Height:</strong> {listing.ganeshaDetails.height}
                      </div>
                    )}
                    {listing.ganeshaDetails.decorationType && (
                      <div>
                        <strong>Decoration:</strong> {listing.ganeshaDetails.decorationType}
                      </div>
                    )}
                    {listing.ganeshaDetails.murtiType && (
                      <div>
                        <strong>Murti Type:</strong> {listing.ganeshaDetails.murtiType}
                      </div>
                    )}
                    {listing.ganeshaDetails.isEcoFriendly && (
                      <div style={{ color: '#10b981', fontWeight: 'bold' }}>
                        🌱 Eco-Friendly Celebration
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Facilities */}
              {listing.facilities && (
                <div>
                  <h4 style={{ color: '#232323', marginBottom: '12px' }}>Facilities</h4>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {listing.facilities.wheelchairAccessible && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        ♿ Wheelchair Accessible
                      </span>
                    )}
                    {listing.facilities.parkingAvailable && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🚗 Parking Available
                      </span>
                    )}
                    {listing.facilities.restrooms && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🚻 Restrooms
                      </span>
                    )}
                    {listing.facilities.foodStalls && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🍽️ Food Stalls
                      </span>
                    )}
                    {listing.facilities.culturalPrograms && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        🎭 Cultural Programs
                      </span>
                    )}
                    {listing.facilities.onlineAarti && (
                      <span style={{ backgroundColor: '#f0f9ff', color: '#0369a1', padding: '4px 12px', borderRadius: '16px', fontSize: '14px' }}>
                        📱 Online Aarti
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'photos' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Photos</h3>
              {listing.images && listing.images.length > 0 ? (
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', 
                  gap: '12px' 
                }}>
                  {listing.images.map((image, index) => (
                    <div key={index} style={{
                      aspectRatio: '1',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      backgroundColor: '#f3f4f6'
                    }}>
                      <img
                        src={`http://localhost:5001${image.url}`}
                        alt={image.caption || `Photo ${index + 1}`}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          cursor: 'pointer'
                        }}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                  No photos available
                </p>
              )}
            </div>
          )}

          {activeTab === 'videos' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Videos</h3>
              <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                Videos coming soon...
              </p>
            </div>
          )}

          {activeTab === 'reviews' && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#232323', marginBottom: '16px', fontSize: '20px' }}>Reviews</h3>
              <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                No reviews yet. Be the first to review!
              </p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div>
          {/* Contact Info */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            marginBottom: '20px'
          }}>
            <h4 style={{ color: '#232323', marginBottom: '16px' }}>Contact Information</h4>
            <div style={{ marginBottom: '12px' }}>
              <strong>📞 Phone:</strong><br />
              <a href={`tel:${listing.contactPhone}`} style={{ color: '#AF0003', textDecoration: 'none' }}>
                {listing.contactPhone}
              </a>
            </div>
            {listing.contactEmail && (
              <div style={{ marginBottom: '12px' }}>
                <strong>✉️ Email:</strong><br />
                <a href={`mailto:${listing.contactEmail}`} style={{ color: '#AF0003', textDecoration: 'none' }}>
                  {listing.contactEmail}
                </a>
              </div>
            )}
            <div>
              <strong>📍 Address:</strong><br />
              <span style={{ color: '#666' }}>{listing.location.address}</span><br />
              <span style={{ color: '#666' }}>
                {listing.location.city}, {listing.location.district}<br />
                {listing.location.state} - {listing.location.pincode}
              </span>
            </div>
          </div>

          {/* Visiting Hours */}
          {listing.visitingHours && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '20px'
            }}>
              <h4 style={{ color: '#232323', marginBottom: '16px' }}>Visiting Hours</h4>
              {listing.visitingHours.isOpen24Hours ? (
                <p style={{ color: '#10b981', fontWeight: 'bold' }}>🕐 Open 24 Hours</p>
              ) : (
                <p style={{ color: '#666' }}>
                  🕐 {listing.visitingHours.openTime} - {listing.visitingHours.closeTime}
                </p>
              )}
            </div>
          )}

          {/* Quick Stats */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h4 style={{ color: '#232323', marginBottom: '16px' }}>Page Stats</h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>👁️ Views:</span>
                <strong>{listing.views || 0}</strong>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>📅 Created:</span>
                <strong>{new Date(listing.createdAt).toLocaleDateString()}</strong>
              </div>
              {listing.publishedAt && (
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>🌐 Published:</span>
                  <strong>{new Date(listing.publishedAt).toLocaleDateString()}</strong>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageView;
