import React, { useState, useEffect } from 'react';

const ListingsPage = () => {
  const [pages, setPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [filters, setFilters] = useState({
    category: 'all',
    search: ''
  });

  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 12
  });

  const [sortBy, setSortBy] = useState('publishedAt');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch pages when filters change
  useEffect(() => {
    fetchPages();
  }, [filters, pagination.currentPage, sortBy, sortOrder]);

  const fetchPages = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...filters,
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        sortBy,
        sortOrder
      });

      const response = await fetch(`http://localhost:5001/api/user-page/published?${queryParams}`);
      if (response.ok) {
        const data = await response.json();
        setPages(data.pages);
        setPagination(data.pagination);
      } else {
        setError('Failed to fetch pages');
      }
    } catch (error) {
      console.error('Error fetching pages:', error);
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      search: ''
    });
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getPageImage = (page) => {
    if (page.coverPhoto) {
      return `http://localhost:5001${page.coverPhoto}`;
    }
    if (page.profileImage) {
      return `http://localhost:5001${page.profileImage}`;
    }
    if (page.coverImage?.url) {
      return `http://localhost:5001${page.coverImage.url}`;
    }
    return null;
  };

  const formatCelebrationStatus = (listing) => {
    const status = listing.celebrationStatus;
    const statusColors = {
      ongoing: '#10b981',
      upcoming: '#f59e0b',
      completed: '#6b7280',
      unknown: '#6b7280'
    };
    
    return {
      text: status.charAt(0).toUpperCase() + status.slice(1),
      color: statusColors[status] || '#6b7280'
    };
  };

  const inputStyle = {
    width: '100%',
    padding: '8px 12px',
    border: '1px solid #e0e0e0',
    borderRadius: '6px',
    fontSize: '14px',
    backgroundColor: 'white'
  };

  const selectStyle = {
    ...inputStyle,
    cursor: 'pointer'
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc',
      padding: '20px 0'
    }}>
      <div style={{ 
        maxWidth: '1400px', 
        margin: '0 auto', 
        padding: '0 20px'
      }}>
        {/* Header */}
        <div style={{ 
          marginBottom: '30px',
          textAlign: 'center'
        }}>
          <h1 style={{
            color: '#AF0003',
            fontSize: '36px',
            marginBottom: '10px',
            fontWeight: 'bold'
          }}>
            🕉️ Ganesha Pages
          </h1>
          <p style={{
            color: '#666',
            fontSize: '18px',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Discover Mandal, Home & Celebrity Ganesha celebrations
          </p>
        </div>

        <div style={{ display: 'flex', gap: '30px' }}>
          {/* Left Sidebar - Filters */}
          <div style={{ 
            width: '300px', 
            flexShrink: 0,
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '25px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            height: 'fit-content',
            position: 'sticky',
            top: '20px'
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <h3 style={{ 
                color: '#232323', 
                margin: 0,
                fontSize: '20px',
                fontWeight: 'bold'
              }}>
                🔍 Filters
              </h3>
              <button
                onClick={clearFilters}
                style={{
                  background: 'none',
                  border: '1px solid #e0e0e0',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  color: '#666',
                  cursor: 'pointer'
                }}
              >
                Clear All
              </button>
            </div>

            {/* Search */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search listings..."
                value={filters.search}
                onChange={handleSearchChange}
                style={inputStyle}
              />
            </div>

            {/* Category Filter */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Categories</option>
                <option value="Mandal">Mandal</option>
                <option value="Home">Home</option>
                <option value="Celebrity">Celebrity</option>
              </select>
            </div>
          </div>

          {/* Main Content */}
          <div style={{ flex: 1 }}>
            {/* Sort and Results Info */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '25px',
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div>
                <span style={{ color: '#666', fontSize: '14px' }}>
                  Showing {pages.length} of {pagination.totalItems} pages
                </span>
              </div>
              
              <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                <span style={{ fontSize: '14px', color: '#666' }}>Sort by:</span>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field);
                    setSortOrder(order);
                  }}
                  style={{ ...selectStyle, width: 'auto', minWidth: '150px' }}
                >
                  <option value="publishedAt-desc">Recently Published</option>
                  <option value="publishedAt-asc">Oldest Published</option>
                  <option value="pageViews-desc">Most Viewed</option>
                  <option value="username-asc">Name A-Z</option>
                  <option value="username-desc">Name Z-A</option>
                </select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div style={{
                backgroundColor: '#fef2f2',
                color: '#dc2626',
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '20px',
                border: '1px solid #fecaca'
              }}>
                {error}
              </div>
            )}

            {/* Loading State */}
            {loading ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔄</div>
                <p style={{ color: '#666', fontSize: '18px' }}>Loading pages...</p>
              </div>
            ) : listings.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔍</div>
                <h3 style={{ color: '#232323', marginBottom: '10px' }}>No pages found</h3>
                <p style={{ color: '#666' }}>No published Ganesha pages match your search</p>
                <button
                  onClick={clearFilters}
                  style={{
                    backgroundColor: '#AF0003',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '12px 24px',
                    fontSize: '16px',
                    cursor: 'pointer',
                    marginTop: '20px'
                  }}
                >
                  Clear All Filters
                </button>
              </div>
            ) : (
              <>
                {/* Pages Grid */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
                  gap: '25px',
                  marginBottom: '40px'
                }}>
                  {pages.map((page) => (
                    <div
                      key={page._id}
                      style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        overflow: 'hidden',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        cursor: 'pointer'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-4px)';
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                      }}
                      onClick={() => {
                        // Navigate to page view
                        window.location.href = `/${page.username}`;
                      }}
                    >
                      {/* Image */}
                      <div style={{ position: 'relative', height: '200px', overflow: 'hidden' }}>
                        {getPageImage(page) ? (
                          <img
                            src={getPageImage(page)}
                            alt={page.mandalInfo?.mandalName || page.username}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        ) : (
                          <div style={{
                            width: '100%',
                            height: '100%',
                            backgroundColor: '#f3f4f6',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '48px',
                            color: '#AF0003'
                          }}>
                            🕉️
                          </div>
                        )}

                        {/* Category Badge */}
                        <div style={{
                          position: 'absolute',
                          top: '12px',
                          left: '12px',
                          backgroundColor: '#AF0003',
                          color: 'white',
                          padding: '6px 12px',
                          borderRadius: '20px',
                          fontSize: '12px',
                          fontWeight: 'bold'
                        }}>
                          {page.category}
                        </div>

                      </div>

                      {/* Content */}
                      <div style={{ padding: '20px' }}>
                        <h3 style={{
                          color: '#232323',
                          margin: '0 0 8px 0',
                          fontSize: '18px',
                          fontWeight: 'bold',
                          lineHeight: '1.3'
                        }}>
                          {page.mandalInfo?.mandalName || page.username}
                        </h3>

                        <p style={{
                          color: '#666',
                          margin: '0 0 12px 0',
                          fontSize: '14px',
                          lineHeight: '1.4',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}>
                          {page.mandalInfo?.description || 'Ganesha celebration page'}
                        </p>

                        {/* Location */}
                        {page.mandalInfo?.location && (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginBottom: '12px'
                          }}>
                            <span style={{ marginRight: '6px' }}>📍</span>
                            <span style={{
                              color: '#666',
                              fontSize: '14px'
                            }}>
                              {page.mandalInfo.location}
                            </span>
                          </div>
                        )}

                        {/* Ganesha Details */}
                        <div style={{ 
                          display: 'flex', 
                          gap: '12px',
                          marginBottom: '12px',
                          flexWrap: 'wrap'
                        }}>
                          {/* Remove Ganesha details for now */}
                        </div>

                        {/* Stats */}
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          paddingTop: '12px',
                          borderTop: '1px solid #f0f0f0'
                        }}>
                          <div style={{ 
                            display: 'flex', 
                            gap: '12px',
                            fontSize: '12px',
                            color: '#666'
                          }}>
                            <span>👁️ {page.pageViews || 0}</span>
                            <span>📅 {new Date(page.publishedAt).toLocaleDateString()}</span>
                          </div>
                          
                          <button style={{
                            backgroundColor: '#AF0003',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '6px 12px',
                            fontSize: '12px',
                            cursor: 'pointer'
                          }}>
                            View Page
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '10px',
                    backgroundColor: 'white',
                    padding: '20px',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                  }}>
                    <button
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={!pagination.hasPrevPage}
                      style={{
                        backgroundColor: pagination.hasPrevPage ? '#AF0003' : '#e5e7eb',
                        color: pagination.hasPrevPage ? 'white' : '#9ca3af',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 16px',
                        fontSize: '14px',
                        cursor: pagination.hasPrevPage ? 'pointer' : 'not-allowed'
                      }}
                    >
                      Previous
                    </button>

                    <span style={{ 
                      color: '#666', 
                      fontSize: '14px',
                      margin: '0 10px'
                    }}>
                      Page {pagination.currentPage} of {pagination.totalPages}
                    </span>

                    <button
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={!pagination.hasNextPage}
                      style={{
                        backgroundColor: pagination.hasNextPage ? '#AF0003' : '#e5e7eb',
                        color: pagination.hasNextPage ? 'white' : '#9ca3af',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 16px',
                        fontSize: '14px',
                        cursor: pagination.hasNextPage ? 'pointer' : 'not-allowed'
                      }}
                    >
                      Next
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListingsPage;
