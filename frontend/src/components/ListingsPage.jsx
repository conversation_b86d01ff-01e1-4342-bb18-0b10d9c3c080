import React, { useState, useEffect } from 'react';

const ListingsPage = () => {
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filterOptions, setFilterOptions] = useState({
    categories: [],
    cities: [],
    districts: [],
    states: [],
    decorationTypes: [],
    murtiTypes: []
  });

  // Filter states
  const [filters, setFilters] = useState({
    category: 'all',
    decorationType: 'all',
    murtiType: 'all',
    city: 'all',
    district: 'all',
    state: 'all',
    isEcoFriendly: false,
    search: ''
  });

  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 12
  });

  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch filter options
  useEffect(() => {
    fetchFilterOptions();
  }, []);

  // Fetch listings when filters change
  useEffect(() => {
    fetchListings();
  }, [filters, pagination.currentPage, sortBy, sortOrder]);

  const fetchFilterOptions = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/listings/filters');
      if (response.ok) {
        const data = await response.json();
        setFilterOptions(data.filters);
      }
    } catch (error) {
      console.error('Error fetching filter options:', error);
    }
  };

  const fetchListings = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...filters,
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        sortBy,
        sortOrder
      });

      const response = await fetch(`http://localhost:5001/api/listings?${queryParams}`);
      if (response.ok) {
        const data = await response.json();
        setListings(data.listings);
        setPagination(data.pagination);
      } else {
        setError('Failed to fetch listings');
      }
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      decorationType: 'all',
      murtiType: 'all',
      city: 'all',
      district: 'all',
      state: 'all',
      isEcoFriendly: false,
      search: ''
    });
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getListingImage = (listing) => {
    if (listing.images && listing.images.length > 0) {
      const primaryImage = listing.images.find(img => img.isPrimary) || listing.images[0];
      return `http://localhost:5001${primaryImage.url}`;
    }
    return '/placeholder-ganesha.jpg'; // You'll need to add this placeholder image
  };

  const formatCelebrationStatus = (listing) => {
    const status = listing.celebrationStatus;
    const statusColors = {
      ongoing: '#10b981',
      upcoming: '#f59e0b',
      completed: '#6b7280',
      unknown: '#6b7280'
    };
    
    return {
      text: status.charAt(0).toUpperCase() + status.slice(1),
      color: statusColors[status] || '#6b7280'
    };
  };

  const inputStyle = {
    width: '100%',
    padding: '8px 12px',
    border: '1px solid #e0e0e0',
    borderRadius: '6px',
    fontSize: '14px',
    backgroundColor: 'white'
  };

  const selectStyle = {
    ...inputStyle,
    cursor: 'pointer'
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc',
      padding: '20px 0'
    }}>
      <div style={{ 
        maxWidth: '1400px', 
        margin: '0 auto', 
        padding: '0 20px'
      }}>
        {/* Header */}
        <div style={{ 
          marginBottom: '30px',
          textAlign: 'center'
        }}>
          <h1 style={{ 
            color: '#AF0003', 
            fontSize: '36px', 
            marginBottom: '10px',
            fontWeight: 'bold'
          }}>
            🕉️ Ganesha Listings
          </h1>
          <p style={{ 
            color: '#666', 
            fontSize: '18px',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Discover beautiful Ganesha celebrations near you
          </p>
        </div>

        <div style={{ display: 'flex', gap: '30px' }}>
          {/* Left Sidebar - Filters */}
          <div style={{ 
            width: '300px', 
            flexShrink: 0,
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '25px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            height: 'fit-content',
            position: 'sticky',
            top: '20px'
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <h3 style={{ 
                color: '#232323', 
                margin: 0,
                fontSize: '20px',
                fontWeight: 'bold'
              }}>
                🔍 Filters
              </h3>
              <button
                onClick={clearFilters}
                style={{
                  background: 'none',
                  border: '1px solid #e0e0e0',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  color: '#666',
                  cursor: 'pointer'
                }}
              >
                Clear All
              </button>
            </div>

            {/* Search */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search listings..."
                value={filters.search}
                onChange={handleSearchChange}
                style={inputStyle}
              />
            </div>

            {/* Category Filter */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Categories</option>
                {filterOptions.categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Decoration Type Filter */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Decoration Type
              </label>
              <select
                value={filters.decorationType}
                onChange={(e) => handleFilterChange('decorationType', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Types</option>
                {filterOptions.decorationTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Murti Type Filter */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                Murti Type
              </label>
              <select
                value={filters.murtiType}
                onChange={(e) => handleFilterChange('murtiType', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Types</option>
                {filterOptions.murtiTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Location Filters */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                State
              </label>
              <select
                value={filters.state}
                onChange={(e) => handleFilterChange('state', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All States</option>
                {filterOptions.states.map(state => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                District
              </label>
              <select
                value={filters.district}
                onChange={(e) => handleFilterChange('district', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Districts</option>
                {filterOptions.districts.map(district => (
                  <option key={district} value={district}>{district}</option>
                ))}
              </select>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: 'bold',
                color: '#232323'
              }}>
                City
              </label>
              <select
                value={filters.city}
                onChange={(e) => handleFilterChange('city', e.target.value)}
                style={selectStyle}
              >
                <option value="all">All Cities</option>
                {filterOptions.cities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>

            {/* Eco-Friendly Filter */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ 
                display: 'flex', 
                alignItems: 'center',
                cursor: 'pointer',
                fontWeight: 'bold',
                color: '#232323'
              }}>
                <input
                  type="checkbox"
                  checked={filters.isEcoFriendly}
                  onChange={(e) => handleFilterChange('isEcoFriendly', e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                🌱 Eco-Friendly Only
              </label>
            </div>
          </div>

          {/* Main Content */}
          <div style={{ flex: 1 }}>
            {/* Sort and Results Info */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '25px',
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div>
                <span style={{ color: '#666', fontSize: '14px' }}>
                  Showing {listings.length} of {pagination.totalItems} listings
                </span>
              </div>
              
              <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                <span style={{ fontSize: '14px', color: '#666' }}>Sort by:</span>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field);
                    setSortOrder(order);
                  }}
                  style={{ ...selectStyle, width: 'auto', minWidth: '150px' }}
                >
                  <option value="createdAt-desc">Newest First</option>
                  <option value="createdAt-asc">Oldest First</option>
                  <option value="views-desc">Most Viewed</option>
                  <option value="ratings.average-desc">Highest Rated</option>
                  <option value="title-asc">Name A-Z</option>
                  <option value="title-desc">Name Z-A</option>
                </select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div style={{
                backgroundColor: '#fef2f2',
                color: '#dc2626',
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '20px',
                border: '1px solid #fecaca'
              }}>
                {error}
              </div>
            )}

            {/* Loading State */}
            {loading ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔄</div>
                <p style={{ color: '#666', fontSize: '18px' }}>Loading listings...</p>
              </div>
            ) : listings.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔍</div>
                <h3 style={{ color: '#232323', marginBottom: '10px' }}>No listings found</h3>
                <p style={{ color: '#666' }}>Try adjusting your filters or search terms</p>
                <button
                  onClick={clearFilters}
                  style={{
                    backgroundColor: '#AF0003',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '12px 24px',
                    fontSize: '16px',
                    cursor: 'pointer',
                    marginTop: '20px'
                  }}
                >
                  Clear All Filters
                </button>
              </div>
            ) : (
              <>
                {/* Listings Grid */}
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))', 
                  gap: '25px',
                  marginBottom: '40px'
                }}>
                  {listings.map((listing) => (
                    <div
                      key={listing._id}
                      style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        overflow: 'hidden',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        cursor: 'pointer'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-4px)';
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                      }}
                      onClick={() => {
                        // Navigate to listing detail page
                        window.location.href = `/listing/${listing._id}`;
                      }}
                    >
                      {/* Image */}
                      <div style={{ position: 'relative', height: '200px', overflow: 'hidden' }}>
                        <img
                          src={getListingImage(listing)}
                          alt={listing.title}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                          onError={(e) => {
                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEMxNzAgNjguOTU0MyAxNjEuMDQ2IDYwIDE1MCA2MEMxMzguOTU0IDYwIDEzMCA2OC45NTQzIDEzMCA4MEMxMzAgOTEuMDQ1NyAxMzguOTU0IDEwMCAxNTAgMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTIwIDEyMEgxODBWMTQwSDE2MEwxNTAgMTMwTDE0MCAxNDBIMTIwVjEyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                          }}
                        />
                        
                        {/* Category Badge */}
                        <div style={{
                          position: 'absolute',
                          top: '12px',
                          left: '12px',
                          backgroundColor: '#AF0003',
                          color: 'white',
                          padding: '6px 12px',
                          borderRadius: '20px',
                          fontSize: '12px',
                          fontWeight: 'bold'
                        }}>
                          {listing.category}
                        </div>

                        {/* Celebration Status */}
                        {listing.celebrationStatus && (
                          <div style={{
                            position: 'absolute',
                            top: '12px',
                            right: '12px',
                            backgroundColor: formatCelebrationStatus(listing).color,
                            color: 'white',
                            padding: '6px 12px',
                            borderRadius: '20px',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            {formatCelebrationStatus(listing).text}
                          </div>
                        )}

                        {/* Eco-Friendly Badge */}
                        {listing.ganeshaDetails?.isEcoFriendly && (
                          <div style={{
                            position: 'absolute',
                            bottom: '12px',
                            left: '12px',
                            backgroundColor: '#10b981',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: 'bold'
                          }}>
                            🌱 Eco-Friendly
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div style={{ padding: '20px' }}>
                        <h3 style={{ 
                          color: '#232323', 
                          margin: '0 0 8px 0',
                          fontSize: '18px',
                          fontWeight: 'bold',
                          lineHeight: '1.3'
                        }}>
                          {listing.title}
                        </h3>
                        
                        <p style={{ 
                          color: '#666', 
                          margin: '0 0 12px 0',
                          fontSize: '14px',
                          lineHeight: '1.4',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}>
                          {listing.description}
                        </p>

                        {/* Location */}
                        <div style={{ 
                          display: 'flex', 
                          alignItems: 'center',
                          marginBottom: '12px'
                        }}>
                          <span style={{ marginRight: '6px' }}>📍</span>
                          <span style={{ 
                            color: '#666', 
                            fontSize: '14px'
                          }}>
                            {listing.location.city}, {listing.location.district}
                          </span>
                        </div>

                        {/* Ganesha Details */}
                        <div style={{ 
                          display: 'flex', 
                          gap: '12px',
                          marginBottom: '12px',
                          flexWrap: 'wrap'
                        }}>
                          {listing.ganeshaDetails?.decorationType && (
                            <span style={{
                              backgroundColor: '#f0f9ff',
                              color: '#0369a1',
                              padding: '4px 8px',
                              borderRadius: '12px',
                              fontSize: '11px',
                              fontWeight: 'bold'
                            }}>
                              {listing.ganeshaDetails.decorationType}
                            </span>
                          )}
                          {listing.ganeshaDetails?.murtiType && (
                            <span style={{
                              backgroundColor: '#fef3c7',
                              color: '#d97706',
                              padding: '4px 8px',
                              borderRadius: '12px',
                              fontSize: '11px',
                              fontWeight: 'bold'
                            }}>
                              {listing.ganeshaDetails.murtiType}
                            </span>
                          )}
                        </div>

                        {/* Stats */}
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          paddingTop: '12px',
                          borderTop: '1px solid #f0f0f0'
                        }}>
                          <div style={{ 
                            display: 'flex', 
                            gap: '12px',
                            fontSize: '12px',
                            color: '#666'
                          }}>
                            <span>👁️ {listing.views || 0}</span>
                            {listing.ratings?.average > 0 && (
                              <span>⭐ {listing.ratings.average.toFixed(1)}</span>
                            )}
                          </div>
                          
                          <span style={{ 
                            fontSize: '12px', 
                            color: '#999'
                          }}>
                            {new Date(listing.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '10px',
                    backgroundColor: 'white',
                    padding: '20px',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                  }}>
                    <button
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={!pagination.hasPrevPage}
                      style={{
                        backgroundColor: pagination.hasPrevPage ? '#AF0003' : '#e5e7eb',
                        color: pagination.hasPrevPage ? 'white' : '#9ca3af',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 16px',
                        fontSize: '14px',
                        cursor: pagination.hasPrevPage ? 'pointer' : 'not-allowed'
                      }}
                    >
                      Previous
                    </button>

                    <span style={{ 
                      color: '#666', 
                      fontSize: '14px',
                      margin: '0 10px'
                    }}>
                      Page {pagination.currentPage} of {pagination.totalPages}
                    </span>

                    <button
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={!pagination.hasNextPage}
                      style={{
                        backgroundColor: pagination.hasNextPage ? '#AF0003' : '#e5e7eb',
                        color: pagination.hasNextPage ? 'white' : '#9ca3af',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '8px 16px',
                        fontSize: '14px',
                        cursor: pagination.hasNextPage ? 'pointer' : 'not-allowed'
                      }}
                    >
                      Next
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListingsPage;
