import React, { useState, useEffect } from 'react';

const VideoLightbox = ({ video, onClose }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        if (isFullscreen) {
          exitFullscreen();
        } else {
          onClose();
        }
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isFullscreen, onClose]);

  const getEmbedUrl = (url, platform) => {
    switch (platform) {
      case 'youtube':
        const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        const videoId = youtubeMatch ? youtubeMatch[1] : '';
        return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
      
      case 'vimeo':
        const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
        const vimeoId = vimeoMatch ? vimeoMatch[1] : '';
        return `https://player.vimeo.com/video/${vimeoId}?autoplay=1`;
      
      case 'facebook':
        return `https://www.facebook.com/plugins/video.php?href=${encodeURIComponent(url)}&show_text=false&autoplay=true`;
      
      case 'instagram':
        // Instagram doesn't support direct embedding, so we'll show a preview
        return null;
      
      default:
        return null;
    }
  };

  const enterFullscreen = () => {
    const element = document.getElementById('video-container');
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }
    setIsFullscreen(true);
  };

  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    setIsFullscreen(false);
  };

  const embedUrl = getEmbedUrl(video.videoUrl || video.url, video.platform);

  return (
    <div 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.95)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 2000,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        id="video-container"
        style={{
          position: 'relative',
          width: isFullscreen ? '100vw' : '90vw',
          height: isFullscreen ? '100vh' : '80vh',
          maxWidth: isFullscreen ? 'none' : '1200px',
          maxHeight: isFullscreen ? 'none' : '675px',
          backgroundColor: '#000',
          borderRadius: isFullscreen ? '0' : '12px',
          overflow: 'hidden'
        }}
      >
        {/* Video Header */}
        {!isFullscreen && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            background: 'linear-gradient(to bottom, rgba(0,0,0,0.8), transparent)',
            padding: '20px',
            zIndex: 10,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start'
          }}>
            <div>
              <h3 style={{ 
                color: 'white', 
                margin: '0 0 5px 0',
                fontSize: '18px',
                fontWeight: 'bold'
              }}>
                {video.title && video.title.trim() !== '' ? video.title : 'Untitled Video'}
              </h3>
              {video.description && (
                <p style={{ 
                  color: 'rgba(255,255,255,0.8)', 
                  margin: 0,
                  fontSize: '14px'
                }}>
                  {video.description}
                </p>
              )}
            </div>
            <div style={{ display: 'flex', gap: '10px' }}>
              <button
                onClick={enterFullscreen}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 12px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
                title="Fullscreen"
              >
                ⛶
              </button>
              <button
                onClick={onClose}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 12px',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
                title="Close"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Video Content */}
        {embedUrl ? (
          <iframe
            src={embedUrl}
            style={{
              width: '100%',
              height: '100%',
              border: 'none'
            }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={video.title}
          />
        ) : (
          // Fallback for platforms that don't support embedding (like Instagram)
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center',
            padding: '40px'
          }}>
            <div style={{ fontSize: '64px', marginBottom: '20px' }}>🎥</div>
            <h3 style={{ marginBottom: '16px' }}>{video.title && video.title.trim() !== '' ? video.title : 'Untitled Video'}</h3>
            {video.description && (
              <p style={{ marginBottom: '24px', opacity: 0.8 }}>{video.description}</p>
            )}
            <p style={{ marginBottom: '24px', opacity: 0.6 }}>
              This {video.platform} video cannot be embedded. Click below to watch on {video.platform}.
            </p>
            <button
              onClick={() => window.open(video.videoUrl || video.url, '_blank')}
              style={{
                backgroundColor: '#AF0003',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '12px 24px',
                cursor: 'pointer',
                fontSize: '16px',
                fontWeight: 'bold'
              }}
            >
              Watch on {video.platform.charAt(0).toUpperCase() + video.platform.slice(1)}
            </button>
          </div>
        )}

        {/* Fullscreen Controls */}
        {isFullscreen && (
          <div style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            zIndex: 20
          }}>
            <button
              onClick={exitFullscreen}
              style={{
                backgroundColor: 'rgba(0,0,0,0.7)',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '10px 15px',
                cursor: 'pointer',
                fontSize: '18px'
              }}
              title="Exit Fullscreen"
            >
              ×
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoLightbox;
