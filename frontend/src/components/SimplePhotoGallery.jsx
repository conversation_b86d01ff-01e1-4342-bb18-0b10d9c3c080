import React, { useState, useEffect } from 'react';
import VideoLightbox from './VideoLightbox';

const SimplePhotoGallery = ({ onBack }) => {
  const [currentView, setCurrentView] = useState('albums'); // 'albums', 'photos', 'videos'
  const [albums, setAlbums] = useState([]);
  const [selectedAlbum, setSelectedAlbum] = useState(null);
  const [albumMedia, setAlbumMedia] = useState([]);
  const [videos, setVideos] = useState([]);
  const [showCreateAlbum, setShowCreateAlbum] = useState(false);
  const [showUploadPhotos, setShowUploadPhotos] = useState(false);
  const [showAddVideo, setShowAddVideo] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [showEditAlbum, setShowEditAlbum] = useState(false);
  const [showEditPhoto, setShowEditPhoto] = useState(false);
  const [editingAlbum, setEditingAlbum] = useState(null);
  const [editingPhoto, setEditingPhoto] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null); // { type: 'album'|'photo', id: string, name: string }
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [albumData, setAlbumData] = useState({
    name: '',
    description: ''
  });

  const [uploadData, setUploadData] = useState({
    title: '',
    description: '',
    files: []
  });

  const [videoData, setVideoData] = useState({
    title: '',
    description: '',
    url: ''
  });

  useEffect(() => {
    // Always fetch albums and videos on component mount for accurate counts
    fetchAlbums();
    fetchVideos();
  }, []);

  // Keyboard navigation for photo lightbox
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (selectedMedia && albumMedia.length > 1) {
        const currentIndex = albumMedia.findIndex(m => m._id === selectedMedia._id);

        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : albumMedia.length - 1;
          setSelectedMedia(albumMedia[prevIndex]);
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          const nextIndex = currentIndex < albumMedia.length - 1 ? currentIndex + 1 : 0;
          setSelectedMedia(albumMedia[nextIndex]);
        }
      }

      if (e.key === 'Escape') {
        if (selectedMedia) {
          setSelectedMedia(null);
        } else if (selectedVideo) {
          setSelectedVideo(null);
        }
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [selectedMedia, selectedVideo, albumMedia]);

  useEffect(() => {
    // Refresh data when view changes
    if (currentView === 'albums') {
      fetchAlbums();
    } else if (currentView === 'videos') {
      fetchVideos();
    }
  }, [currentView]);

  const fetchAlbums = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/gallery/albums', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAlbums(data.albums || []);
      }
    } catch (error) {
      console.error('Error fetching albums:', error);
      setError('Failed to load albums');
    }
  };

  const fetchAlbumMedia = async (albumId) => {
    console.log('fetchAlbumMedia called with albumId:', albumId);
    try {
      const token = localStorage.getItem('token');
      console.log('Making API call to:', `http://localhost:5001/api/gallery/albums/${albumId}/media`);

      const response = await fetch(`http://localhost:5001/api/gallery/albums/${albumId}/media`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Album media response:', data);
        console.log('Media array length:', data.media?.length);
        setAlbumMedia(data.media || []);
        setSelectedAlbum(data.album);
        setCurrentView('photos');
      } else {
        console.error('Response not ok:', response.status, response.statusText);
        const errorData = await response.text();
        console.error('Error response:', errorData);
      }
    } catch (error) {
      console.error('Error fetching album media:', error);
      setError('Failed to load album photos');
    }
  };

  const fetchVideos = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/gallery/videos', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setVideos(data.videos || []);
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      setError('Failed to load videos');
    }
  };

  const createAlbum = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/gallery/albums', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(albumData)
      });

      if (response.ok) {
        setSuccess('Album created successfully!');
        setAlbumData({ name: '', description: '' });
        setShowCreateAlbum(false);
        fetchAlbums();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to create album');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const uploadPhotos = async (e) => {
    e.preventDefault();
    if (!selectedAlbum || uploadData.files.length === 0) {
      setError('Please select an album and choose photos to upload');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const formData = new FormData();
      
      uploadData.files.forEach(file => {
        formData.append('photos', file);
      });
      
      formData.append('title', uploadData.title);
      formData.append('description', uploadData.description);

      const response = await fetch(`http://localhost:5001/api/gallery/albums/${selectedAlbum._id}/photos`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        setSuccess(`${uploadData.files.length} photo(s) uploaded successfully!`);
        setUploadData({ title: '', description: '', files: [] });
        setShowUploadPhotos(false);
        fetchAlbumMedia(selectedAlbum._id);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to upload photos');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const addVideo = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const platform = detectPlatform(videoData.url);
      if (!platform) {
        setError('Please enter a valid YouTube, Vimeo, Facebook, or Instagram video URL');
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/gallery/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...videoData,
          platform
        })
      });

      if (response.ok) {
        setSuccess('Video added successfully!');
        setVideoData({ title: '', description: '', url: '' });
        setShowAddVideo(false);
        fetchVideos();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to add video');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  // Update Album
  const updateAlbum = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/albums/${editingAlbum._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(albumData)
      });

      if (response.ok) {
        setSuccess('Album updated successfully!');
        setAlbumData({ name: '', description: '' });
        setShowEditAlbum(false);
        setEditingAlbum(null);
        fetchAlbums();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to update album');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  // Delete Album
  const deleteAlbum = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/albums/${deleteTarget.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Album deleted successfully!');
        setShowDeleteConfirm(false);
        setDeleteTarget(null);
        if (currentView === 'photos' && selectedAlbum?._id === deleteTarget.id) {
          setCurrentView('albums');
          setSelectedAlbum(null);
          setAlbumMedia([]);
        }
        fetchAlbums();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete album');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  // Update Photo
  const updatePhoto = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/media/${editingPhoto._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          title: uploadData.title,
          description: uploadData.description
        })
      });

      if (response.ok) {
        setSuccess('Photo updated successfully!');
        setUploadData({ title: '', description: '', files: [] });
        setShowEditPhoto(false);
        setEditingPhoto(null);
        fetchAlbumMedia(selectedAlbum._id);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to update photo');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  // Delete Photo
  const deletePhoto = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/media/${deleteTarget.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Photo deleted successfully!');
        setShowDeleteConfirm(false);
        setDeleteTarget(null);
        fetchAlbumMedia(selectedAlbum._id);
        fetchAlbums(); // Refresh albums to update cover images and counts
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete photo');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  // Helper functions for CRUD operations
  const handleEditAlbum = (album) => {
    setEditingAlbum(album);
    setAlbumData({ name: album.name, description: album.description || '' });
    setShowEditAlbum(true);
  };

  const handleEditPhoto = (photo) => {
    setEditingPhoto(photo);
    setUploadData({ title: photo.title || '', description: photo.description || '', files: [] });
    setShowEditPhoto(true);
  };

  const handleDeleteAlbum = (album) => {
    setDeleteTarget({ type: 'album', id: album._id, name: album.name });
    setShowDeleteConfirm(true);
  };

  const handleDeletePhoto = (photo) => {
    setDeleteTarget({ type: 'photo', id: photo._id, name: photo.title || 'Untitled Photo' });
    setShowDeleteConfirm(true);
  };

  const handleEditVideo = (video) => {
    setEditingPhoto(video); // Reuse the same state for video editing
    setVideoData({
      title: video.title || '',
      description: video.description || '',
      url: video.videoUrl || video.url || ''
    });
    setShowEditPhoto(true); // Reuse the same modal
  };

  const handleDeleteVideo = (video) => {
    setDeleteTarget({ type: 'video', id: video._id, name: video.title || 'Untitled Video' });
    setShowDeleteConfirm(true);
  };

  const updateVideo = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const platform = detectPlatform(videoData.url);
      if (!platform) {
        setError('Please enter a valid YouTube, Vimeo, Facebook, or Instagram video URL');
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/videos/${editingPhoto._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          title: videoData.title,
          description: videoData.description,
          url: videoData.url,
          platform
        })
      });

      if (response.ok) {
        setSuccess('Video updated successfully!');
        setVideoData({ title: '', description: '', url: '' });
        setShowEditPhoto(false);
        setEditingPhoto(null);
        fetchVideos();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to update video');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const deleteVideo = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/gallery/videos/${deleteTarget.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Video deleted successfully!');
        setShowDeleteConfirm(false);
        setDeleteTarget(null);
        fetchVideos();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete video');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const confirmDelete = () => {
    if (deleteTarget.type === 'album') {
      deleteAlbum();
    } else if (deleteTarget.type === 'photo') {
      deletePhoto();
    } else if (deleteTarget.type === 'video') {
      deleteVideo();
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setUploadData(prev => ({
      ...prev,
      files: files
    }));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUploadData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAlbumInputChange = (e) => {
    const { name, value } = e.target;
    setAlbumData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVideoInputChange = (e) => {
    const { name, value } = e.target;
    setVideoData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const detectPlatform = (url) => {
    if (!url) return null;

    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return 'youtube';
    } else if (url.includes('vimeo.com')) {
      return 'vimeo';
    } else if (url.includes('facebook.com')) {
      return 'facebook';
    } else if (url.includes('instagram.com')) {
      return 'instagram';
    }
    return null;
  };

  const extractVideoId = (url, platform) => {
    let videoId = '';
    switch (platform) {
      case 'youtube':
        const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        videoId = youtubeMatch ? youtubeMatch[1] : '';
        break;
      case 'vimeo':
        const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
        videoId = vimeoMatch ? vimeoMatch[1] : '';
        break;
      case 'facebook':
        videoId = url;
        break;
      case 'instagram':
        videoId = url;
        break;
      default:
        videoId = url;
    }
    return videoId;
  };

  const getVideoThumbnail = (url, platform) => {
    const videoId = extractVideoId(url, platform);
    switch (platform) {
      case 'youtube':
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
      case 'vimeo':
        return `https://vumbnail.com/${videoId}.jpg`;
      default:
        return '/api/placeholder/400/300';
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px 16px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '16px',
    marginBottom: '16px',
    boxSizing: 'border-box'
  };

  return (
    <div style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '30px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <button
            onClick={onBack}
            style={{
              backgroundColor: 'transparent',
              border: '1px solid #e0e0e0',
              padding: '8px 16px',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '20px'
            }}
          >
            ← Back
          </button>
          <h2 style={{ color: '#AF0003', margin: 0 }}>
            📸 {currentView === 'albums' ? 'Photo Albums' : currentView === 'photos' ? `${selectedAlbum?.name || 'Album'} Photos` : 'Video Gallery'}
          </h2>
        </div>
        
        <div style={{ display: 'flex', gap: '10px' }}>
          {currentView === 'albums' && (
            <button
              onClick={() => setShowCreateAlbum(true)}
              style={{
                backgroundColor: '#F2A71B',
                color: 'white',
                padding: '12px 24px',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              + Create Album
            </button>
          )}
          
          {currentView === 'photos' && selectedAlbum && (
            <button
              onClick={() => setShowUploadPhotos(true)}
              style={{
                backgroundColor: '#AF0003',
                color: 'white',
                padding: '12px 24px',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              + Add Photos
            </button>
          )}
          
          {currentView === 'videos' && (
            <button
              onClick={() => setShowAddVideo(true)}
              style={{
                backgroundColor: '#AF0003',
                color: 'white',
                padding: '12px 24px',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              + Add Video
            </button>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div style={{ 
        display: 'flex', 
        gap: '20px', 
        marginBottom: '30px',
        borderBottom: '2px solid #f0f0f0',
        paddingBottom: '10px'
      }}>
        <button
          onClick={() => setCurrentView('albums')}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            padding: '10px 20px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: currentView === 'albums' ? 'bold' : 'normal',
            color: currentView === 'albums' ? '#AF0003' : '#666',
            borderBottom: currentView === 'albums' ? '3px solid #AF0003' : 'none'
          }}
        >
          📁 Albums ({albums.length})
        </button>
        
        <button
          onClick={() => setCurrentView('videos')}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            padding: '10px 20px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: currentView === 'videos' ? 'bold' : 'normal',
            color: currentView === 'videos' ? '#AF0003' : '#666',
            borderBottom: currentView === 'videos' ? '3px solid #AF0003' : 'none'
          }}
        >
          🎥 Videos ({videos.length})
        </button>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '12px 16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {success && (
        <div style={{
          backgroundColor: '#f0fdf4',
          color: '#166534',
          padding: '12px 16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #bbf7d0'
        }}>
          {success}
        </div>
      )}

      {/* Main Content Area */}
      {currentView === 'albums' && (
        <div>
          {albums.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '60px 20px', color: '#666' }}>
              <div style={{ fontSize: '64px', marginBottom: '20px' }}>📁</div>
              <h3 style={{ color: '#232323', marginBottom: '10px' }}>No Albums Yet</h3>
              <p>Create your first album to organize your photos</p>
              <button
                onClick={() => setShowCreateAlbum(true)}
                style={{
                  backgroundColor: '#AF0003',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  marginTop: '20px',
                  fontWeight: 'bold'
                }}
              >
                + Create First Album
              </button>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
              gap: '20px'
            }}>
              {albums.map((album) => (
                <div
                  key={album._id}
                  onClick={() => {
                    console.log('Album clicked:', album._id, album.name);
                    fetchAlbumMedia(album._id);
                  }}
                  style={{
                    border: '1px solid #e0e0e0',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                    backgroundColor: 'white'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-4px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <div style={{
                    height: '200px',
                    backgroundColor: '#f8f9fa',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}>
                    {album.coverImage ? (
                      <img
                        src={`http://localhost:5001${album.coverImage}`}
                        alt={album.name}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                    ) : (
                      <div style={{ textAlign: 'center', color: '#999' }}>
                        <div style={{ fontSize: '48px', marginBottom: '10px' }}>📷</div>
                        <div>No photos yet</div>
                      </div>
                    )}
                    <div style={{
                      position: 'absolute',
                      bottom: '10px',
                      right: '10px',
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px'
                    }}>
                      {album.mediaCount || 0} photos
                    </div>
                  </div>
                  <div style={{ padding: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                      <h3 style={{ margin: 0, color: '#232323', fontSize: '18px', flex: 1 }}>
                        {album.name}
                      </h3>
                      <div style={{ display: 'flex', gap: '8px', marginLeft: '10px' }}>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditAlbum(album);
                          }}
                          style={{
                            backgroundColor: '#F2A71B',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '4px 8px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                          title="Edit Album"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteAlbum(album);
                          }}
                          style={{
                            backgroundColor: '#dc2626',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '4px 8px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                          title="Delete Album"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    {album.description && (
                      <p style={{ margin: '0 0 12px 0', color: '#666', fontSize: '14px', lineHeight: '1.4' }}>
                        {album.description}
                      </p>
                    )}
                    <div style={{
                      fontSize: '12px',
                      color: '#999'
                    }}>
                      Created {new Date(album.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Photos View */}
      {currentView === 'photos' && selectedAlbum && (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
            <button
              onClick={() => setCurrentView('albums')}
              style={{
                backgroundColor: 'transparent',
                border: '1px solid #e0e0e0',
                padding: '8px 16px',
                borderRadius: '8px',
                cursor: 'pointer',
                marginRight: '20px'
              }}
            >
              ← Back to Albums
            </button>
            <h3 style={{ color: '#232323', margin: 0 }}>
              {selectedAlbum.name} ({albumMedia.length} photos)
            </h3>
          </div>

          {albumMedia.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '60px 20px', color: '#666' }}>
              <div style={{ fontSize: '64px', marginBottom: '20px' }}>📷</div>
              <h3 style={{ color: '#232323', marginBottom: '10px' }}>No Photos Yet</h3>
              <p>Upload your first photos to this album</p>
              <button
                onClick={() => setShowUploadPhotos(true)}
                style={{
                  backgroundColor: '#AF0003',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  marginTop: '20px',
                  fontWeight: 'bold'
                }}
              >
                + Upload Photos
              </button>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: '15px'
            }}>
              {albumMedia.map((media) => (
                <div
                  key={media._id}
                  style={{
                    borderRadius: '8px',
                    overflow: 'hidden',
                    backgroundColor: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    position: 'relative',
                    transition: 'transform 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.02)';
                    e.currentTarget.querySelector('.photo-actions').style.opacity = '1';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                    e.currentTarget.querySelector('.photo-actions').style.opacity = '0';
                  }}
                >
                  <div style={{ position: 'relative' }}>
                    <img
                      src={`http://localhost:5001${media.url}`}
                      alt={media.title || 'Photo'}
                      style={{
                        width: '100%',
                        height: '200px',
                        objectFit: 'cover',
                        cursor: 'pointer'
                      }}
                      onClick={() => setSelectedMedia(media)}
                    />

                    {/* Photo Actions Overlay */}
                    <div
                      className="photo-actions"
                      style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        display: 'flex',
                        gap: '4px',
                        opacity: '0',
                        transition: 'opacity 0.2s ease'
                      }}
                    >
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPhoto(media);
                        }}
                        style={{
                          backgroundColor: 'rgba(242, 167, 27, 0.9)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="Edit Photo"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePhoto(media);
                        }}
                        style={{
                          backgroundColor: 'rgba(220, 38, 38, 0.9)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="Delete Photo"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  {(media.title || media.description) && (
                    <div style={{ padding: '8px' }}>
                      {media.title && (
                        <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#232323', marginBottom: '4px' }}>
                          {media.title}
                        </div>
                      )}
                      {media.description && (
                        <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.3' }}>
                          {media.description}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Videos View */}
      {currentView === 'videos' && (
        <div>
          {videos.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '60px 20px', color: '#666' }}>
              <div style={{ fontSize: '64px', marginBottom: '20px' }}>🎥</div>
              <h3 style={{ color: '#232323', marginBottom: '10px' }}>No Videos Yet</h3>
              <p>Add your first video from YouTube, Facebook, Instagram, or Vimeo</p>
              <button
                onClick={() => setShowAddVideo(true)}
                style={{
                  backgroundColor: '#AF0003',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  marginTop: '20px',
                  fontWeight: 'bold'
                }}
              >
                + Add First Video
              </button>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '20px'
            }}>
              {videos.map((video) => (
                <div
                  key={video._id}
                  style={{
                    border: '1px solid #e0e0e0',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    backgroundColor: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    transition: 'transform 0.2s ease',
                    position: 'relative'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.querySelector('.video-actions').style.opacity = '1';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.querySelector('.video-actions').style.opacity = '0';
                  }}
                >
                  <div style={{ position: 'relative' }}>
                    <img
                      src={getVideoThumbnail(video.videoUrl || video.url, video.platform)}
                      alt={video.title || 'Video'}
                      style={{
                        width: '100%',
                        height: '180px',
                        objectFit: 'cover'
                      }}
                    />

                    {/* Play Button */}
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      borderRadius: '50%',
                      width: '60px',
                      height: '60px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => setSelectedVideo(video)}
                    >
                      <div style={{ color: 'white', fontSize: '24px' }}>▶️</div>
                    </div>

                    {/* Platform Badge */}
                    <div style={{
                      position: 'absolute',
                      top: '10px',
                      left: '10px',
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      textTransform: 'capitalize'
                    }}>
                      {video.platform}
                    </div>

                    {/* Video Actions */}
                    <div
                      className="video-actions"
                      style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        display: 'flex',
                        gap: '4px',
                        opacity: '0',
                        transition: 'opacity 0.2s ease'
                      }}
                    >
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditVideo(video);
                        }}
                        style={{
                          backgroundColor: 'rgba(242, 167, 27, 0.9)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="Edit Video"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteVideo(video);
                        }}
                        style={{
                          backgroundColor: 'rgba(220, 38, 38, 0.9)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="Delete Video"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  <div style={{ padding: '16px' }}>
                    <h3 style={{
                      margin: '0 0 8px 0',
                      color: '#232323',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      lineHeight: '1.3'
                    }}>
                      {video.title || 'Untitled Video'}
                    </h3>
                    {video.description && (
                      <p style={{
                        margin: '0 0 12px 0',
                        color: '#666',
                        fontSize: '14px',
                        lineHeight: '1.4',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}>
                        {video.description}
                      </p>
                    )}
                    <div style={{
                      fontSize: '12px',
                      color: '#999',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span>Added {new Date(video.createdAt).toLocaleDateString()}</span>
                      <span style={{
                        backgroundColor: '#f0f0f0',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        fontSize: '10px',
                        textTransform: 'uppercase'
                      }}>
                        {video.platform}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Create Album Modal */}
      {showCreateAlbum && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: '#232323', marginBottom: '20px' }}>Create New Album</h3>

            <form onSubmit={createAlbum}>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Album Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={albumData.name}
                  onChange={handleAlbumInputChange}
                  style={inputStyle}
                  required
                  placeholder="Enter album name"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Description
                </label>
                <textarea
                  name="description"
                  value={albumData.description}
                  onChange={handleAlbumInputChange}
                  style={{ ...inputStyle, height: '80px', resize: 'vertical' }}
                  placeholder="Describe this album (optional)"
                />
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowCreateAlbum(false)}
                  style={{
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    backgroundColor: loading ? '#ccc' : '#AF0003',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  {loading ? 'Creating...' : 'Create Album'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Album Modal */}
      {showEditAlbum && editingAlbum && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: '#232323', marginBottom: '20px' }}>Edit Album</h3>

            <form onSubmit={updateAlbum}>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Album Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={albumData.name}
                  onChange={handleAlbumInputChange}
                  style={inputStyle}
                  required
                  placeholder="Enter album name"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Description
                </label>
                <textarea
                  name="description"
                  value={albumData.description}
                  onChange={handleAlbumInputChange}
                  style={{ ...inputStyle, height: '80px', resize: 'vertical' }}
                  placeholder="Describe this album (optional)"
                />
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowEditAlbum(false);
                    setEditingAlbum(null);
                    setAlbumData({ name: '', description: '' });
                  }}
                  style={{
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    backgroundColor: loading ? '#ccc' : '#AF0003',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  {loading ? 'Updating...' : 'Update Album'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Upload Photos Modal */}
      {showUploadPhotos && selectedAlbum && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: '#232323', marginBottom: '20px' }}>
              Upload Photos to "{selectedAlbum.name}"
            </h3>

            <form onSubmit={uploadPhotos}>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Select Photos *
                </label>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  style={{
                    ...inputStyle,
                    padding: '12px',
                    border: '2px dashed #e0e0e0',
                    borderRadius: '8px',
                    textAlign: 'center',
                    cursor: 'pointer'
                  }}
                  required
                />
                {uploadData.files.length > 0 && (
                  <div style={{ marginTop: '10px', fontSize: '14px', color: '#10b981' }}>
                    ✓ {uploadData.files.length} file(s) selected
                  </div>
                )}
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Title (Optional)
                </label>
                <input
                  type="text"
                  name="title"
                  value={uploadData.title}
                  onChange={handleInputChange}
                  style={inputStyle}
                  placeholder="Enter a title for these photos"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Description (Optional)
                </label>
                <textarea
                  name="description"
                  value={uploadData.description}
                  onChange={handleInputChange}
                  style={{ ...inputStyle, height: '80px', resize: 'vertical' }}
                  placeholder="Describe these photos"
                />
              </div>



              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowUploadPhotos(false);
                    setUploadData({ title: '', description: '', files: [] });
                  }}
                  style={{
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || uploadData.files.length === 0}
                  style={{
                    backgroundColor: loading || uploadData.files.length === 0 ? '#ccc' : '#AF0003',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: loading || uploadData.files.length === 0 ? 'not-allowed' : 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  {loading ? 'Uploading...' : `Upload ${uploadData.files.length} Photo(s)`}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Video Modal */}
      {showAddVideo && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: '#232323', marginBottom: '20px' }}>Add Video</h3>

            <form onSubmit={addVideo}>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Video URL *
                </label>
                <input
                  type="url"
                  name="url"
                  value={videoData.url}
                  onChange={handleVideoInputChange}
                  style={inputStyle}
                  required
                  placeholder="Paste YouTube, Vimeo, Facebook, or Instagram video URL"
                />
                <small style={{ color: '#666', fontSize: '12px' }}>
                  Platform will be automatically detected from the URL
                </small>
                {videoData.url && (
                  <div style={{ marginTop: '8px', fontSize: '12px' }}>
                    {detectPlatform(videoData.url) ? (
                      <span style={{ color: '#10b981' }}>
                        ✓ Detected: {detectPlatform(videoData.url).charAt(0).toUpperCase() + detectPlatform(videoData.url).slice(1)}
                      </span>
                    ) : (
                      <span style={{ color: '#dc2626' }}>
                        ⚠️ Please enter a valid YouTube, Vimeo, Facebook, or Instagram URL
                      </span>
                    )}
                  </div>
                )}
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={videoData.title}
                  onChange={handleVideoInputChange}
                  style={inputStyle}
                  required
                  placeholder="Enter video title"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Description (Optional)
                </label>
                <textarea
                  name="description"
                  value={videoData.description}
                  onChange={handleVideoInputChange}
                  style={{ ...inputStyle, height: '80px', resize: 'vertical' }}
                  placeholder="Describe this video"
                />
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddVideo(false);
                    setVideoData({ title: '', description: '', url: '' });
                  }}
                  style={{
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    backgroundColor: loading ? '#ccc' : '#AF0003',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  {loading ? 'Adding...' : 'Add Video'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Photo Modal */}
      {showEditPhoto && editingPhoto && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: '#232323', marginBottom: '20px' }}>
              {editingPhoto?.type === 'video' ? 'Edit Video' : 'Edit Photo'}
            </h3>

            <div style={{ display: 'flex', gap: '20px', marginBottom: '20px' }}>
              <div style={{ flex: '0 0 150px' }}>
                {editingPhoto?.type === 'video' ? (
                  <img
                    src={getVideoThumbnail(editingPhoto.videoUrl || editingPhoto.url, editingPhoto.platform)}
                    alt={editingPhoto.title || 'Video'}
                    style={{
                      width: '100%',
                      height: '150px',
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                  />
                ) : (
                  <img
                    src={`http://localhost:5001${editingPhoto.url}`}
                    alt={editingPhoto.title || 'Photo'}
                    style={{
                      width: '100%',
                      height: '150px',
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                  />
                )}
              </div>
              <div style={{ flex: 1 }}>
                <form onSubmit={editingPhoto?.type === 'video' ? updateVideo : updatePhoto}>
                  {editingPhoto?.type === 'video' && (
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                        Video URL *
                      </label>
                      <input
                        type="url"
                        name="url"
                        value={videoData.url}
                        onChange={handleVideoInputChange}
                        style={inputStyle}
                        placeholder="Video URL"
                        required
                      />
                      {videoData.url && (
                        <div style={{ marginTop: '4px', fontSize: '12px' }}>
                          {detectPlatform(videoData.url) ? (
                            <span style={{ color: '#10b981' }}>
                              ✓ {detectPlatform(videoData.url).charAt(0).toUpperCase() + detectPlatform(videoData.url).slice(1)}
                            </span>
                          ) : (
                            <span style={{ color: '#dc2626' }}>⚠️ Invalid URL</span>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  <div style={{ marginBottom: '16px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Title
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={editingPhoto?.type === 'video' ? videoData.title : uploadData.title}
                      onChange={editingPhoto?.type === 'video' ? handleVideoInputChange : handleInputChange}
                      style={inputStyle}
                      placeholder={`Enter ${editingPhoto?.type === 'video' ? 'video' : 'photo'} title`}
                    />
                  </div>

                  <div style={{ marginBottom: '20px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={editingPhoto?.type === 'video' ? videoData.description : uploadData.description}
                      onChange={editingPhoto?.type === 'video' ? handleVideoInputChange : handleInputChange}
                      style={{ ...inputStyle, height: '80px', resize: 'vertical' }}
                      placeholder={`Describe this ${editingPhoto?.type === 'video' ? 'video' : 'photo'}`}
                    />
                  </div>

                  <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                    <button
                      type="button"
                      onClick={() => {
                        setShowEditPhoto(false);
                        setEditingPhoto(null);
                        setUploadData({ title: '', description: '', files: [] });
                      }}
                      style={{
                        backgroundColor: '#f5f5f5',
                        color: '#666',
                        border: 'none',
                        padding: '12px 24px',
                        borderRadius: '8px',
                        cursor: 'pointer'
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      style={{
                        backgroundColor: loading ? '#ccc' : '#AF0003',
                        color: 'white',
                        border: 'none',
                        padding: '12px 24px',
                        borderRadius: '8px',
                        cursor: loading ? 'not-allowed' : 'pointer',
                        fontWeight: 'bold'
                      }}
                    >
                      {loading ? 'Updating...' : 'Update Photo'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && deleteTarget && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '400px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>
              {deleteTarget.type === 'album' ? '📁' : '📷'}
            </div>
            <h3 style={{ color: '#232323', marginBottom: '16px' }}>
              Delete {deleteTarget.type === 'album' ? 'Album' : 'Photo'}?
            </h3>
            <p style={{ color: '#666', marginBottom: '24px', lineHeight: '1.5' }}>
              Are you sure you want to delete "{deleteTarget.name}"?
              {deleteTarget.type === 'album' && ' This will also delete all photos in this album.'}
              This action cannot be undone.
            </p>

            <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeleteTarget(null);
                }}
                style={{
                  backgroundColor: '#f5f5f5',
                  color: '#666',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                disabled={loading}
                style={{
                  backgroundColor: loading ? '#ccc' : '#dc2626',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  fontWeight: 'bold'
                }}
              >
                {loading ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Photo Modal for viewing */}
      {selectedMedia && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.95)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2000
        }}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            setSelectedMedia(null);
          }
        }}
        >
          <div style={{
            width: '100vw',
            height: '100vh',
            position: 'relative',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {/* Navigation Arrows */}
            {albumMedia.length > 1 && (
              <>
                <button
                  onClick={() => {
                    const currentIndex = albumMedia.findIndex(m => m._id === selectedMedia._id);
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : albumMedia.length - 1;
                    setSelectedMedia(albumMedia[prevIndex]);
                  }}
                  style={{
                    position: 'absolute',
                    left: '20px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '50px',
                    height: '50px',
                    cursor: 'pointer',
                    fontSize: '24px',
                    zIndex: 10
                  }}
                  title="Previous Image"
                >
                  ‹
                </button>
                <button
                  onClick={() => {
                    const currentIndex = albumMedia.findIndex(m => m._id === selectedMedia._id);
                    const nextIndex = currentIndex < albumMedia.length - 1 ? currentIndex + 1 : 0;
                    setSelectedMedia(albumMedia[nextIndex]);
                  }}
                  style={{
                    position: 'absolute',
                    right: '20px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '50px',
                    height: '50px',
                    cursor: 'pointer',
                    fontSize: '24px',
                    zIndex: 10
                  }}
                  title="Next Image"
                >
                  ›
                </button>
              </>
            )}

            {/* Main Image */}
            <img
              src={`http://localhost:5001${selectedMedia.url}`}
              alt={selectedMedia.title || 'Photo'}
              style={{
                maxWidth: '90vw',
                maxHeight: '85vh',
                objectFit: 'contain',
                borderRadius: '8px'
              }}
            />

            {/* Top Controls */}
            <div style={{
              position: 'absolute',
              top: '20px',
              left: '20px',
              right: '20px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              zIndex: 10
            }}>
              <div style={{
                backgroundColor: 'rgba(0,0,0,0.7)',
                color: 'white',
                padding: '12px 16px',
                borderRadius: '8px',
                maxWidth: '60%'
              }}>
                <h3 style={{ margin: '0 0 4px 0', fontSize: '18px' }}>
                  {selectedMedia.title || 'Untitled Photo'}
                </h3>
                {selectedMedia.description && (
                  <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>
                    {selectedMedia.description}
                  </p>
                )}
                {albumMedia.length > 1 && (
                  <p style={{ margin: '8px 0 0 0', fontSize: '12px', opacity: 0.7 }}>
                    {albumMedia.findIndex(m => m._id === selectedMedia._id) + 1} of {albumMedia.length}
                  </p>
                )}
              </div>

              <button
                onClick={() => setSelectedMedia(null)}
                style={{
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '40px',
                  height: '40px',
                  cursor: 'pointer',
                  fontSize: '20px'
                }}
                title="Close"
              >
                ×
              </button>
            </div>

            {/* Bottom Info */}
            <div style={{
              position: 'absolute',
              bottom: '20px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: 'rgba(0,0,0,0.7)',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '12px',
              opacity: 0.8
            }}>
              Press ESC to close {albumMedia.length > 1 && '• Use arrow keys to navigate'}
            </div>
          </div>
        </div>
      )}

      {/* Video Lightbox */}
      {selectedVideo && (
        <VideoLightbox
          video={selectedVideo}
          onClose={() => setSelectedVideo(null)}
        />
      )}
    </div>
  );
};

export default SimplePhotoGallery;
