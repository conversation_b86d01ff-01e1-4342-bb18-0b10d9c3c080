import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const PagePreview = ({ onBack }) => {
  const { user } = useAuth();
  const [pageData, setPageData] = useState(null);
  const [pageStatus, setPageStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchPageData();
    fetchPageStatus();
  }, []);

  const fetchPageData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/user-page/${user.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPageData(data.page);
      } else {
        setError('Failed to load page data');
      }
    } catch (error) {
      console.error('Error fetching page data:', error);
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const fetchPageStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/user-page/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPageStatus(data);
      }
    } catch (error) {
      console.error('Error fetching page status:', error);
    }
  };

  const handlePublish = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/user-page/publish', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Page published successfully!');
        fetchPageStatus();
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to publish page');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
  };

  const handleUnpublish = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/api/user-page/unpublish', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setSuccess('Page moved to draft successfully!');
        fetchPageStatus();
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to unpublish page');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
  };

  const getCoverImage = () => {
    if (pageData?.user?.coverPhoto) {
      return `http://localhost:5001${pageData.user.coverPhoto}`;
    }
    if (pageData?.gallery?.albums && Object.keys(pageData.gallery.albums).length > 0) {
      const firstAlbum = Object.values(pageData.gallery.albums)[0];
      if (firstAlbum.length > 0) {
        return `http://localhost:5001${firstAlbum[0].url}`;
      }
    }
    return null;
  };

  const getProfileImage = () => {
    if (pageData?.user?.profileImage) {
      return `http://localhost:5001${pageData.user.profileImage}`;
    }
    return null;
  };

  if (loading) {
    return (
      <div style={{ padding: '20px' }}>
        <div style={{ textAlign: 'center', padding: '60px' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔄</div>
          <p style={{ color: '#666', fontSize: '18px' }}>Loading page preview...</p>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div style={{ padding: '20px' }}>
        <div style={{ textAlign: 'center', padding: '60px' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>❌</div>
          <h3 style={{ color: '#232323', marginBottom: '10px' }}>No Page Data</h3>
          <p style={{ color: '#666' }}>Complete your profile to preview your page</p>
          <button
            onClick={onBack}
            style={{
              backgroundColor: '#AF0003',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Go Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <div>
          <button
            onClick={onBack}
            style={{
              backgroundColor: 'transparent',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              padding: '8px 16px',
              cursor: 'pointer',
              marginRight: '16px'
            }}
          >
            ← Back
          </button>
          <span style={{ fontSize: '24px', fontWeight: 'bold', color: '#232323' }}>
            Page Preview
          </span>
        </div>
        
        <div style={{ display: 'flex', gap: '12px' }}>
          {pageStatus?.pageStatus === 'published' ? (
            <>
              <button
                onClick={() => window.open(`/page/${user.id}`, '_blank')}
                style={{
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  cursor: 'pointer'
                }}
              >
                🌐 View Live Page
              </button>
              <button
                onClick={handleUnpublish}
                style={{
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  cursor: 'pointer'
                }}
              >
                📝 Move to Draft
              </button>
            </>
          ) : (
            <button
              onClick={handlePublish}
              style={{
                backgroundColor: '#AF0003',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '12px 24px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              🌐 Publish Page
            </button>
          )}
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div style={{
          backgroundColor: '#f0f9ff',
          color: '#0369a1',
          padding: '16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #bae6fd'
        }}>
          {success}
        </div>
      )}

      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '16px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {/* Page Status Card */}
      {pageStatus && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          marginBottom: '30px'
        }}>
          <h3 style={{ color: '#232323', marginBottom: '16px' }}>Page Status</h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px', marginBottom: '20px' }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#AF0003' }}>
                {pageStatus.pageStatus === 'published' ? '🌐' : '📝'}
              </div>
              <div style={{ fontSize: '14px', color: '#666', textTransform: 'capitalize' }}>
                {pageStatus.pageStatus}
              </div>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
                {pageStatus.completionPercentage}%
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                Complete
              </div>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f59e0b' }}>
                {pageStatus.pageViews || 0}
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                Views
              </div>
            </div>
          </div>

          {/* Completion Checklist */}
          <div style={{ marginTop: '20px' }}>
            <h4 style={{ color: '#232323', marginBottom: '12px' }}>Completion Checklist:</h4>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
              {Object.entries(pageStatus.completionStatus).map(([key, completed]) => (
                <div key={key} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ color: completed ? '#10b981' : '#dc2626' }}>
                    {completed ? '✅' : '❌'}
                  </span>
                  <span style={{ fontSize: '14px', color: '#666', textTransform: 'capitalize' }}>
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Page Preview */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        border: '2px dashed #e0e0e0'
      }}>
        {/* Cover Photo */}
        <div style={{
          height: '300px',
          backgroundColor: '#f3f4f6',
          backgroundImage: getCoverImage() ? `url(${getCoverImage()})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}>
          {!getCoverImage() && (
            <div style={{ fontSize: '72px', color: '#AF0003' }}>🕉️</div>
          )}
          
          {/* Preview Badge */}
          <div style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            backgroundColor: 'rgba(175, 0, 3, 0.9)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '20px',
            fontSize: '14px',
            fontWeight: 'bold'
          }}>
            👁️ PREVIEW
          </div>
        </div>

        {/* Profile Section */}
        <div style={{ padding: '30px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px', marginBottom: '20px' }}>
            {/* Profile Image */}
            <div style={{
              width: '100px',
              height: '100px',
              borderRadius: '50%',
              backgroundColor: '#f3f4f6',
              backgroundImage: getProfileImage() ? `url(${getProfileImage()})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '36px',
              color: '#AF0003',
              border: '4px solid white',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
            }}>
              {!getProfileImage() && '🕉️'}
            </div>

            {/* Page Info */}
            <div>
              <h1 style={{ color: '#232323', fontSize: '28px', margin: '0 0 8px 0' }}>
                {pageData.mandalInfo?.mandalName || pageData.user.username}
              </h1>
              <p style={{ color: '#666', margin: '0 0 8px 0' }}>
                📍 {pageData.mandalInfo?.location || 'Location not specified'}
              </p>
              <div style={{ display: 'flex', gap: '16px', fontSize: '14px', color: '#666' }}>
                <span>📸 {pageData.stats.totalPhotos} photos</span>
                <span>🎥 {pageData.stats.totalVideos} videos</span>
                <span>👥 {pageData.stats.teamMembers} team members</span>
              </div>
            </div>
          </div>

          {/* Description */}
          {pageData.mandalInfo?.description && (
            <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>
              {pageData.mandalInfo.description}
            </p>
          )}

          {/* Quick Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '16px',
            padding: '20px',
            backgroundColor: '#f8fafc',
            borderRadius: '8px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#AF0003' }}>
                {pageData.user.category}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>Category</div>
            </div>
            
            {pageData.mandalInfo?.establishmentYear && (
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#10b981' }}>
                  {pageData.mandalInfo.establishmentYear}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>Established</div>
              </div>
            )}
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#f59e0b' }}>
                {pageData.stats.totalPhotos + pageData.stats.totalVideos}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>Media Items</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PagePreview;
