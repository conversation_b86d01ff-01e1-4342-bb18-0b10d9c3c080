import React, { useState } from 'react';

const CreateListing = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'Mandal',
    ownerName: '',
    contactPhone: '',
    contactEmail: '',
    location: {
      address: '',
      city: '',
      district: '',
      state: 'Maharashtra',
      pincode: ''
    },
    ganeshaDetails: {
      height: '',
      decorationType: 'Traditional',
      murtiType: 'Clay',
      isEcoFriendly: false,
      celebrationDuration: {
        startDate: '',
        endDate: '',
        days: 1
      }
    },
    facilities: {
      wheelchairAccessible: false,
      parkingAvailable: false,
      restrooms: false,
      foodStalls: false,
      culturalPrograms: false,
      onlineAarti: false
    },
    visitingHours: {
      openTime: '06:00',
      closeTime: '22:00',
      isOpen24Hours: false
    }
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (e, status = 'draft') => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const listingData = {
        ...formData,
        status
      };

      const response = await fetch('http://localhost:5001/api/listings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(listingData)
      });

      if (response.ok) {
        const data = await response.json();
        if (status === 'published') {
          setSuccess('Page published successfully! Redirecting...');
          setTimeout(() => {
            window.location.href = `/page/${data.listing._id}`;
          }, 2000);
        } else {
          setSuccess('Draft saved successfully! Redirecting...');
          setTimeout(() => {
            window.location.href = '/dashboard';
          }, 2000);
        }
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to create listing');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }
    setLoading(false);
  };

  const inputStyle = {
    width: '100%',
    padding: '12px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '14px',
    backgroundColor: 'white'
  };

  const selectStyle = {
    ...inputStyle,
    cursor: 'pointer'
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc',
      padding: '20px 0'
    }}>
      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto', 
        padding: '0 20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '30px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ 
            color: '#AF0003', 
            fontSize: '28px', 
            marginBottom: '10px',
            textAlign: 'center'
          }}>
            🕉️ Create New Listing
          </h1>
          <p style={{ 
            color: '#666', 
            textAlign: 'center',
            marginBottom: '30px'
          }}>
            Share your Ganesha celebration with devotees
          </p>

          {success && (
            <div style={{
              backgroundColor: '#f0f9ff',
              color: '#0369a1',
              padding: '16px',
              borderRadius: '8px',
              marginBottom: '20px',
              border: '1px solid #bae6fd'
            }}>
              {success}
            </div>
          )}

          {error && (
            <div style={{
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              padding: '16px',
              borderRadius: '8px',
              marginBottom: '20px',
              border: '1px solid #fecaca'
            }}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Basic Information */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Basic Information</h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  style={inputStyle}
                  required
                  placeholder="e.g., Lalbaugcha Raja 2024"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  style={{ ...inputStyle, height: '100px', resize: 'vertical' }}
                  required
                  placeholder="Describe your Ganesha celebration..."
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Category *
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    style={selectStyle}
                    required
                  >
                    <option value="Mandal">Mandal</option>
                    <option value="Home">Home</option>
                    <option value="Celebrity">Celebrity</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Owner Name *
                  </label>
                  <input
                    type="text"
                    name="ownerName"
                    value={formData.ownerName}
                    onChange={handleInputChange}
                    style={inputStyle}
                    required
                    placeholder="Your name"
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginTop: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Contact Phone *
                  </label>
                  <input
                    type="tel"
                    name="contactPhone"
                    value={formData.contactPhone}
                    onChange={handleInputChange}
                    style={inputStyle}
                    required
                    placeholder="10-digit phone number"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Contact Email
                  </label>
                  <input
                    type="email"
                    name="contactEmail"
                    value={formData.contactEmail}
                    onChange={handleInputChange}
                    style={inputStyle}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            {/* Location */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Location</h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  Address *
                </label>
                <input
                  type="text"
                  name="location.address"
                  value={formData.location.address}
                  onChange={handleInputChange}
                  style={inputStyle}
                  required
                  placeholder="Full address"
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    City *
                  </label>
                  <input
                    type="text"
                    name="location.city"
                    value={formData.location.city}
                    onChange={handleInputChange}
                    style={inputStyle}
                    required
                    placeholder="City"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    District *
                  </label>
                  <input
                    type="text"
                    name="location.district"
                    value={formData.location.district}
                    onChange={handleInputChange}
                    style={inputStyle}
                    required
                    placeholder="District"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Pincode *
                  </label>
                  <input
                    type="text"
                    name="location.pincode"
                    value={formData.location.pincode}
                    onChange={handleInputChange}
                    style={inputStyle}
                    required
                    placeholder="6-digit pincode"
                  />
                </div>
              </div>
            </div>

            {/* Ganesha Details */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#232323', marginBottom: '20px' }}>Ganesha Details</h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Height
                  </label>
                  <input
                    type="text"
                    name="ganeshaDetails.height"
                    value={formData.ganeshaDetails.height}
                    onChange={handleInputChange}
                    style={inputStyle}
                    placeholder="e.g., 5 feet"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Decoration Type
                  </label>
                  <select
                    name="ganeshaDetails.decorationType"
                    value={formData.ganeshaDetails.decorationType}
                    onChange={handleInputChange}
                    style={selectStyle}
                  >
                    <option value="Traditional">Traditional</option>
                    <option value="Modern">Modern</option>
                    <option value="Eco-Friendly">Eco-Friendly</option>
                    <option value="Theme-Based">Theme-Based</option>
                    <option value="Minimalist">Minimalist</option>
                    <option value="Grand">Grand</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                    Murti Type
                  </label>
                  <select
                    name="ganeshaDetails.murtiType"
                    value={formData.ganeshaDetails.murtiType}
                    onChange={handleInputChange}
                    style={selectStyle}
                  >
                    <option value="Clay">Clay</option>
                    <option value="Plaster of Paris">Plaster of Paris</option>
                    <option value="Eco-Friendly">Eco-Friendly</option>
                    <option value="Fiber">Fiber</option>
                    <option value="Metal">Metal</option>
                    <option value="Stone">Stone</option>
                  </select>
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontWeight: 'bold'
                }}>
                  <input
                    type="checkbox"
                    name="ganeshaDetails.isEcoFriendly"
                    checked={formData.ganeshaDetails.isEcoFriendly}
                    onChange={handleInputChange}
                    style={{ marginRight: '8px' }}
                  />
                  🌱 Eco-Friendly Celebration
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '16px',
              marginTop: '40px'
            }}>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  handleSubmit(e, 'draft');
                }}
                disabled={loading}
                style={{
                  backgroundColor: loading ? '#ccc' : '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '16px 32px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  minWidth: '180px'
                }}
              >
                {loading ? 'Saving...' : '📝 Save as Draft'}
              </button>

              <button
                type="submit"
                onClick={(e) => {
                  e.preventDefault();
                  handleSubmit(e, 'published');
                }}
                disabled={loading}
                style={{
                  backgroundColor: loading ? '#ccc' : '#AF0003',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '16px 32px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  minWidth: '180px'
                }}
              >
                {loading ? 'Publishing...' : '🌐 Publish Page'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateListing;
