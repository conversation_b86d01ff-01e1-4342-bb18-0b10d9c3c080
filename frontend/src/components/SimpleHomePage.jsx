import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const SimpleHomePage = () => {
  const { user } = useAuth();
  const [listings, setListings] = useState([]);
  const [filteredListings, setFilteredListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');

  useEffect(() => {
    fetchListings();
  }, []);

  useEffect(() => {
    filterListings();
  }, [listings, searchTerm, selectedCategory, selectedLocation]);

  const fetchListings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/listings');
      if (response.ok) {
        const data = await response.json();
        setListings(data.listings || []);
      }
    } catch (error) {
      console.error('Error fetching listings:', error);
    }
    setLoading(false);
  };

  const filterListings = () => {
    let filtered = listings;

    if (searchTerm) {
      filtered = filtered.filter(listing =>
        listing.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        listing.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        listing.location?.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        listing.location?.district?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(listing => listing.category === selectedCategory);
    }

    if (selectedLocation !== 'all') {
      filtered = filtered.filter(listing =>
        listing.location?.city?.toLowerCase().includes(selectedLocation.toLowerCase()) ||
        listing.location?.district?.toLowerCase().includes(selectedLocation.toLowerCase())
      );
    }

    setFilteredListings(filtered);
  };

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'mandal_ganesha', label: 'Mandal Ganesha' },
    { value: 'home_ganesha', label: 'Home Ganesha' },
    { value: 'celebrity_ganesha', label: 'Celebrity Ganesha' }
  ];

  const locations = [
    { value: 'all', label: 'All Locations' },
    { value: 'mumbai', label: 'Mumbai' },
    { value: 'pune', label: 'Pune' },
    { value: 'nashik', label: 'Nashik' },
    { value: 'nagpur', label: 'Nagpur' }
  ];

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', minHeight: '100vh' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '16px 0',
        position: 'sticky',
        top: 0,
        zIndex: 100
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <h1 style={{ color: '#AF0003', margin: 0, fontSize: '24px' }}>
              🕉️ Ganesh Darshan
            </h1>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {user ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ color: '#666' }}>Welcome, {user.username}!</span>
                <button
                  onClick={() => window.location.href = '/dashboard'}
                  style={{
                    backgroundColor: '#AF0003',
                    color: 'white',
                    padding: '8px 16px',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  Dashboard
                </button>
              </div>
            ) : (
              <div style={{ display: 'flex', gap: '12px' }}>
                <button
                  onClick={() => window.location.href = '/login'}
                  style={{
                    backgroundColor: 'transparent',
                    color: '#AF0003',
                    padding: '8px 16px',
                    border: '1px solid #AF0003',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  Login
                </button>
                <button
                  onClick={() => window.location.href = '/register'}
                  style={{
                    backgroundColor: '#AF0003',
                    color: 'white',
                    padding: '8px 16px',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  Register
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section style={{
        background: 'linear-gradient(135deg, #AF0003 0%, #F2A71B 100%)',
        color: 'white',
        padding: '60px 20px',
        textAlign: 'center'
      }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <h2 style={{ fontSize: '48px', margin: '0 0 16px 0', fontWeight: 'bold' }}>
            Discover Divine Ganesha Celebrations
          </h2>
          <p style={{ fontSize: '20px', margin: '0 0 32px 0', opacity: 0.9 }}>
            Find and explore Ganesha celebrations near you. Join the festivities and experience the divine blessings.
          </p>
          
          {/* Search Bar */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '8px',
            display: 'flex',
            maxWidth: '600px',
            margin: '0 auto',
            boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
          }}>
            <input
              type="text"
              placeholder="Search by mandal name or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                flex: 1,
                border: 'none',
                padding: '12px 16px',
                fontSize: '16px',
                borderRadius: '8px',
                outline: 'none'
              }}
            />
            <button style={{
              backgroundColor: '#AF0003',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}>
              🔍 Search
            </button>
          </div>
        </div>
      </section>

      {/* Filters */}
      <section style={{
        backgroundColor: '#f9fafb',
        padding: '20px',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          gap: '20px',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold', color: '#333' }}>
              Category:
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            >
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold', color: '#333' }}>
              Location:
            </label>
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            >
              {locations.map(loc => (
                <option key={loc.value} value={loc.value}>{loc.label}</option>
              ))}
            </select>
          </div>

          <div style={{ marginLeft: 'auto' }}>
            <span style={{ color: '#666', fontSize: '14px' }}>
              {filteredListings.length} results found
            </span>
          </div>
        </div>
      </section>

      {/* Listings */}
      <section style={{ padding: '40px 20px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {loading ? (
            <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🕉️</div>
              <p>Loading Ganesha celebrations...</p>
            </div>
          ) : filteredListings.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
              <p>No celebrations found matching your criteria.</p>
              <p>Try adjusting your search or filters.</p>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
              gap: '24px'
            }}>
              {filteredListings.map((listing) => (
                <div
                  key={listing._id}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                    overflow: 'hidden',
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-4px)';
                    e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                  }}
                >
                  {/* Image */}
                  <div style={{
                    height: '200px',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '48px',
                    color: '#AF0003',
                    position: 'relative'
                  }}>
                    {listing.images && listing.images.length > 0 ? (
                      <img
                        src={`http://localhost:5001${listing.images[0].url}`}
                        alt={listing.title}
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.parentNode.innerHTML = '🕉️';
                        }}
                      />
                    ) : (
                      '🕉️'
                    )}

                    {/* Category Badge */}
                    <div style={{
                      position: 'absolute',
                      top: '12px',
                      left: '12px',
                      backgroundColor: '#AF0003',
                      color: 'white',
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      {listing.category}
                    </div>
                  </div>

                  {/* Content */}
                  <div style={{ padding: '20px' }}>
                    <h3 style={{ margin: '0 0 8px 0', color: '#232323', fontSize: '18px', fontWeight: 'bold' }}>
                      {listing.title || 'Ganesha Celebration'}
                    </h3>

                    <p style={{ margin: '0 0 12px 0', color: '#666', fontSize: '14px' }}>
                      📍 {listing.location?.city}, {listing.location?.district}
                    </p>

                    <p style={{
                      margin: '0 0 16px 0',
                      color: '#666',
                      fontSize: '14px',
                      lineHeight: '1.4',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}>
                      {listing.description || 'Experience divine blessings and join the celebration.'}
                    </p>

                    {/* Ganesha Details */}
                    <div style={{
                      display: 'flex',
                      gap: '8px',
                      marginBottom: '16px',
                      flexWrap: 'wrap'
                    }}>
                      {listing.ganeshaDetails?.decorationType && (
                        <span style={{
                          backgroundColor: '#f0f9ff',
                          color: '#0369a1',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '11px',
                          fontWeight: 'bold'
                        }}>
                          {listing.ganeshaDetails.decorationType}
                        </span>
                      )}
                      {listing.ganeshaDetails?.murtiType && (
                        <span style={{
                          backgroundColor: '#fef3c7',
                          color: '#d97706',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '11px',
                          fontWeight: 'bold'
                        }}>
                          {listing.ganeshaDetails.murtiType}
                        </span>
                      )}
                      {listing.ganeshaDetails?.isEcoFriendly && (
                        <span style={{
                          backgroundColor: '#dcfce7',
                          color: '#166534',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '11px',
                          fontWeight: 'bold'
                        }}>
                          🌱 Eco-Friendly
                        </span>
                      )}
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <button
                          onClick={() => window.location.href = `/listing/${listing._id}`}
                          style={{
                            backgroundColor: '#AF0003',
                            color: 'white',
                            border: 'none',
                            padding: '8px 16px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '14px'
                          }}
                        >
                          View Details
                        </button>
                        <button style={{
                          backgroundColor: 'transparent',
                          color: '#AF0003',
                          border: '1px solid #AF0003',
                          padding: '8px 16px',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '14px'
                        }}>
                          ❤️ Favorite
                        </button>
                      </div>
                      
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        {listing.duration || '5 days'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* View All Listings Button */}
          <div style={{
            textAlign: 'center',
            marginTop: '40px',
            paddingTop: '40px',
            borderTop: '1px solid #e0e0e0'
          }}>
            <button
              onClick={() => window.location.href = '/listings'}
              style={{
                backgroundColor: '#AF0003',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                padding: '16px 32px',
                fontSize: '18px',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 12px rgba(175, 0, 3, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 6px 20px rgba(175, 0, 3, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 4px 12px rgba(175, 0, 3, 0.3)';
              }}
            >
              🔍 View All Listings
            </button>
            <p style={{
              color: '#666',
              fontSize: '14px',
              marginTop: '12px',
              margin: '12px 0 0 0'
            }}>
              Explore thousands of Ganesha celebrations with advanced filters
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{
        backgroundColor: '#232323',
        color: 'white',
        padding: '40px 20px',
        textAlign: 'center'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h3 style={{ color: '#AF0003', marginBottom: '16px' }}>🕉️ Ganesh Darshan</h3>
          <p style={{ margin: '0 0 16px 0', color: '#ccc' }}>
            Connecting devotees with divine Ganesha celebrations across India
          </p>
          <p style={{ margin: 0, fontSize: '14px', color: '#999' }}>
            © 2024 Ganesh Darshan. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default SimpleHomePage;
