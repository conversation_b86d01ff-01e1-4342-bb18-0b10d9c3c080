import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import SimpleLogin from './components/SimpleLogin';
import SimpleRegister from './components/SimpleRegister';
import SimpleDashboard from './components/SimpleDashboard';
import SimpleCategorySelection from './components/SimpleCategorySelection';
import SimpleSuperAdmin from './components/SimpleSuperAdmin';
import SimpleHomePage from './components/SimpleHomePage';
import ListingsPage from './components/ListingsPage';
import PageView from './components/PageView';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, isLoading } = useAuth();

  console.log('ProtectedRoute: user =', user, 'isLoading =', isLoading);

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>🕉️</div>
          <div style={{ color: '#AF0003' }}>Loading...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};





// Simple Test Page Component
const TestPage = () => (
  <div style={{ padding: '30px', fontFamily: 'Arial, sans-serif' }}>
    <h1 style={{ color: '#AF0003' }}>Test Page</h1>
    <p>This is a simple test page to verify routing works.</p>
    <button
      style={{
        backgroundColor: '#F2A71B',
        color: 'white',
        padding: '10px 20px',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer'
      }}
      onClick={() => window.location.href = '/'}
    >
      Back to Home
    </button>
  </div>
);

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/" element={<SimpleHomePage />} />
          <Route path="/login" element={<SimpleLogin />} />
          <Route path="/register" element={<SimpleRegister />} />
          <Route
            path="/category-selection"
            element={
              <ProtectedRoute>
                <SimpleCategorySelection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <SimpleDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/super-admin"
            element={
              <ProtectedRoute>
                <SimpleSuperAdmin />
              </ProtectedRoute>
            }
          />
          <Route path="/listings" element={<ListingsPage />} />
          <Route path="/page/:id" element={<PageView />} />
          <Route path="/test" element={<TestPage />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
